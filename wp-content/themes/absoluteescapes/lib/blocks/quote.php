<?php 

/**
 * Quote
 */

$text = get_sub_field('text');
$label = get_sub_field('label');
// $review_embed = get_field('review_embed', 'options');
$review_embed_fallback = get_field('review_embed_fallback', 'options');

?>

<section class="quote">
    <div class="quote__inner" data-aos="fade">
        <div class="container quote__container">
            <div class="quote__content centre inner-container">
            <?php if (function_exists('aito_has_review_data') && aito_has_review_data()) : ?>
                <div class="review-bar">
                    <div class="review-bar__review">
                        <?php aito_display_widget(); ?>
                    </div>
                </div>
            <?php endif; ?>
                <?php if($text) : ?>
                    <blockquote class="content-area">
                        <p><?php echo $text; ?></p>
                    </blockquote>
                <?php endif; ?>
                <?php if($label) : ?>
                    <span class="quote__label heading-light"><?php echo $label; ?></span>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>