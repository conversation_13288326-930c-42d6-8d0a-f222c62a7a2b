jQuery(document).ready(function($) {
    
    // Test connection button
    $('#aito-test-connection').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');
        
        $button.prop('disabled', true).text(aitoReviews.strings.testing);
        $results.html('<div class="notice notice-info"><p>' + aitoReviews.strings.testing + '</p></div>');
        
        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_test_connection',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    var html = '<div class="notice notice-success"><p><strong>' + aitoReviews.strings.success + '</strong> ' + response.message + '</p>';

                    // Show review methods if available (prioritized)
                    if (response.review_methods && response.review_methods.length > 0) {
                        html += '<div style="margin: 15px 0;">';
                        html += '<h4>🎯 Available Review Methods (' + response.review_count + '):</h4>';
                        html += '<div style="margin-top: 8px;">';
                        response.review_methods.forEach(function(method) {
                            html += '<code style="display: inline-block; margin: 2px 4px; padding: 4px 8px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 3px; font-size: 12px;">' + method + '</code>';
                        });
                        html += '</div>';
                        html += '</div>';
                    }

                    // Show all feedback methods if available
                    if (response.feedback_methods && response.feedback_methods.length > 0) {
                        html += '<div style="margin: 15px 0;">';
                        html += '<h4>📊 All Feedback Methods (' + response.feedback_count + '):</h4>';
                        html += '<div style="margin-top: 8px;">';
                        response.feedback_methods.forEach(function(method) {
                            html += '<code style="display: inline-block; margin: 2px 4px; padding: 3px 6px; background: #fff2e7; border: 1px solid #ffd6b3; border-radius: 3px; font-size: 11px;">' + method + '</code>';
                        });
                        html += '</div>';
                        html += '</div>';
                    }

                    // Show extracted method names (collapsed by default)
                    if (response.method_names && response.method_names.length > 0) {
                        html += '<div style="margin: 15px 0;">';
                        html += '<h4>🔧 All SOAP Methods (' + response.total_method_names + ') <button type="button" class="button-link aito-toggle-methods" style="font-size: 12px;">Show/Hide</button></h4>';
                        html += '<div class="aito-all-methods" style="display: none; max-height: 150px; overflow-y: auto; background: #f9f9f9; padding: 10px; border: 1px solid #ddd; margin: 8px 0; border-radius: 3px;">';
                        response.method_names.forEach(function(method) {
                            html += '<code style="display: inline-block; margin: 1px 2px; padding: 1px 4px; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 2px; font-size: 10px; max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">' + method + '</code>';
                        });
                        html += '</div>';
                        html += '</div>';
                    }

                    // Show total methods count
                    if (response.total_methods) {
                        html += '<p><strong>Total SOAP Functions:</strong> ' + response.total_methods + ' | <strong>Extracted Methods:</strong> ' + response.total_method_names + '</p>';
                    }

                    html += '</div>';
                    $results.html(html);
                } else {
                    $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + response.message + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('Test Connection');
            }
        });
    });
    
    // Fetch data now button
    $('#aito-fetch-now').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        $button.prop('disabled', true).text(aitoReviews.strings.fetching);
        $results.html('<div class="notice notice-info"><p>' + aitoReviews.strings.fetching + '</p></div>');

        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_fetch_now',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    var message = '<strong>' + aitoReviews.strings.success + '</strong> ' + response.data.message;
                    if (response.data.score !== undefined && response.data.total !== undefined) {
                        message += '<br><strong>Score:</strong> ' + response.data.score + '<br><strong>Total:</strong> ' + response.data.total;
                    }

                    var html = '<div class="notice notice-success"><p>' + message + '</p></div>';

                    // Add debug information if available
                    if (response.data.debug) {
                        html += formatDebugInfo(response.data.debug, 'success');
                    }

                    $results.html(html);

                    // Refresh the page to show updated data
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                } else {
                    var html = '<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + response.data.message + '</p></div>';

                    // Add debug information if available
                    if (response.data.debug) {
                        html += formatDebugInfo(response.data.debug, 'error');
                    }

                    $results.html(html);
                }
            },
            error: function(xhr, status, error) {
                var html = '<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>';

                // Try to parse response for debug info
                try {
                    var errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.data && errorResponse.data.debug) {
                        html += formatDebugInfo(errorResponse.data.debug, 'error');
                    }
                } catch (e) {
                    // Ignore JSON parse errors
                }

                $results.html(html);
            },
            complete: function() {
                $button.prop('disabled', false).text('Fetch Data Now');
            }
        });
    });

    // Function to format debug information
    function formatDebugInfo(debug, type) {
        var html = '<div class="aito-debug-info ' + type + '">';
        html += '<h4>Debug Information <button type="button" class="button-link aito-toggle-debug">Show/Hide</button></h4>';
        html += '<div class="aito-debug-content" style="display: none;">';

        // Timestamp
        if (debug.timestamp) {
            html += '<p><strong>Timestamp:</strong> ' + debug.timestamp + '</p>';
        }

        // Settings
        if (debug.settings) {
            html += '<h5>Settings:</h5>';
            html += '<ul>';
            for (var key in debug.settings) {
                html += '<li><strong>' + key + ':</strong> ' + debug.settings[key] + '</li>';
            }
            html += '</ul>';
        }

        // Connection test
        if (debug.connection_test) {
            html += '<h5>Connection Test:</h5>';
            html += '<p><strong>Success:</strong> ' + (debug.connection_test.success ? 'Yes' : 'No') + '</p>';
            html += '<p><strong>Message:</strong> ' + debug.connection_test.message + '</p>';
        }

        // Fetch timing
        if (debug.fetch_duration) {
            html += '<p><strong>Fetch Duration:</strong> ' + debug.fetch_duration + '</p>';
        }

        // Data
        if (debug.data) {
            html += '<h5>Retrieved Data:</h5>';
            html += '<pre>' + JSON.stringify(debug.data, null, 2) + '</pre>';
        }

        // Storage info
        if (debug.storage) {
            html += '<h5>Storage Information:</h5>';
            html += '<pre>' + JSON.stringify(debug.storage, null, 2) + '</pre>';
        }

        // Error details
        if (debug.error) {
            html += '<h5>Error Details:</h5>';
            html += '<p class="error-message">' + debug.error + '</p>';
        }

        // Exception details
        if (debug.exception) {
            html += '<h5>Exception Details:</h5>';
            html += '<p><strong>Message:</strong> ' + debug.exception.message + '</p>';
            html += '<p><strong>File:</strong> ' + debug.exception.file + ':' + debug.exception.line + '</p>';
            if (debug.exception.trace) {
                html += '<h6>Stack Trace:</h6>';
                html += '<pre class="stack-trace">' + debug.exception.trace + '</pre>';
            }
        }

        html += '</div>';
        html += '</div>';

        return html;
    }

    // Toggle debug info visibility
    $(document).on('click', '.aito-toggle-debug', function() {
        $(this).closest('.aito-debug-info').find('.aito-debug-content').toggle();
    });

    // Toggle methods list visibility
    $(document).on('click', '.aito-toggle-methods', function() {
        $(this).closest('div').find('.aito-all-methods').toggle();
    });

    // Clear debug log button
    $('#aito-clear-debug').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        if (!confirm('Are you sure you want to clear the debug log?')) {
            return;
        }

        $button.prop('disabled', true).text('Clearing...');

        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_clear_debug',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    $results.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');

                    // Hide debug log section
                    $('.aito-debug-log-section').fadeOut();

                    // Refresh after 2 seconds
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + response.data.message + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('Clear Debug Log');
            }
        });
    });
    
    // Auto-save settings when changed
    $('input[name="aito_reviews_enabled"]').on('change', function() {
        var $checkbox = $(this);
        var isEnabled = $checkbox.is(':checked');
        
        // Show/hide cron interval field based on enabled status
        var $intervalRow = $('select[name="aito_reviews_cron_interval"]').closest('tr');
        if (isEnabled) {
            $intervalRow.show();
        } else {
            $intervalRow.hide();
        }
    });
    
    // Initialize visibility on page load
    var isEnabled = $('input[name="aito_reviews_enabled"]').is(':checked');
    var $intervalRow = $('select[name="aito_reviews_cron_interval"]').closest('tr');
    if (!isEnabled) {
        $intervalRow.hide();
    }
    
    // Form validation
    $('form').on('submit', function(e) {
        var endpointUrl = $('input[name="aito_reviews_endpoint_url"]').val();
        
        if (!endpointUrl) {
            alert('Please enter an API endpoint URL.');
            e.preventDefault();
            return false;
        }
        
        // Basic URL validation
        try {
            new URL(endpointUrl);
        } catch (error) {
            alert('Please enter a valid URL for the API endpoint.');
            e.preventDefault();
            return false;
        }
    });
    
    // Add tooltips for help text
    $('.description').each(function() {
        var $desc = $(this);
        var $input = $desc.prev('input, select, textarea');
        
        if ($input.length) {
            $input.attr('title', $desc.text());
        }
    });
    
    // Status refresh functionality
    if ($('.aito-status-info').length) {
        setInterval(function() {
            // Auto-refresh status every 30 seconds if on the settings page
            if (document.visibilityState === 'visible') {
                // Only refresh if no AJAX is currently running
                if (!$('#aito-test-connection').prop('disabled') && !$('#aito-fetch-now').prop('disabled')) {
                    // Could add auto-refresh logic here if needed
                }
            }
        }, 30000);
    }

    // Test authentication button
    $('#aito-test-auth').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        $button.prop('disabled', true).text('Testing Authentication...');
        $results.html('<div class="notice notice-info"><p>Testing authentication methods...</p></div>');

        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_test_auth',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    var html = '<div class="notice notice-success"><p><strong>✅ Authentication Successful!</strong> ' + response.data.message + '</p>';

                    if (response.data.data) {
                        html += '<h4>Retrieved Data:</h4>';
                        html += '<pre style="background: #f0f8ff; padding: 10px; border: 1px solid #0073aa; border-radius: 3px; max-height: 200px; overflow-y: auto;">';
                        html += JSON.stringify(response.data.data, null, 2);
                        html += '</pre>';
                    }

                    html += '</div>';
                    $results.html(html);
                } else {
                    var html = '<div class="notice notice-error"><p><strong>❌ Authentication Failed:</strong> ' + response.data.message + '</p>';

                    if (response.data.error) {
                        html += '<h4>Error Details:</h4>';
                        html += '<p style="background: #fff2f2; padding: 8px; border: 1px solid #d63638; border-radius: 3px;">' + response.data.error + '</p>';
                    }

                    if (response.data.debug_log && response.data.debug_log.length > 0) {
                        html += '<h4>Recent Debug Log:</h4>';
                        html += '<div style="background: #f9f9f9; padding: 8px; border: 1px solid #ddd; border-radius: 3px; max-height: 150px; overflow-y: auto;">';
                        response.data.debug_log.forEach(function(entry) {
                            html += '<div style="font-family: monospace; font-size: 11px; margin: 2px 0;">' + entry + '</div>';
                        });
                        html += '</div>';
                    }

                    if (response.data.exception) {
                        html += '<h4>Exception Details:</h4>';
                        html += '<p><strong>Message:</strong> ' + response.data.exception.message + '</p>';
                        html += '<p><strong>Location:</strong> ' + response.data.exception.file + ':' + response.data.exception.line + '</p>';
                    }

                    html += '</div>';
                    $results.html(html);
                }
            },
            error: function(xhr, status, error) {
                $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('Test Authentication');
            }
        });
    });



    // Clear session button
    $('#aito-clear-session').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        if (!confirm('Are you sure you want to clear the current session? The next API call will need to re-authenticate.')) {
            return;
        }

        $button.prop('disabled', true).text('Clearing Session...');

        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_clear_session',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    $results.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');

                    // Refresh after 2 seconds to show updated session status
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + response.data.message + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('Clear Session');
            }
        });
    });

    // Clear debug log button
    $('#aito-clear-debug').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        $button.prop('disabled', true).text('Clearing Debug Log...');

        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_clear_debug',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    $results.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                } else {
                    $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + response.data.message + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('Clear Debug Log');
            }
        });
    });



    // View debug file button
    $('#aito-view-debug-file').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        $button.prop('disabled', true).text('Loading Debug File...');

        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_view_debug_file',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    var html = '<div class="notice notice-info"><p><strong>📄 Debug File Contents:</strong></p>';
                    html += '<p>' + response.data.message + '</p>';

                    if (response.data.file_exists && response.data.content) {
                        html += '<div style="background: #f9f9f9; padding: 10px; border: 1px solid #ddd; margin: 8px 0; border-radius: 3px; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 11px; white-space: pre-wrap;">';
                        html += response.data.content;
                        html += '</div>';

                        html += '<p><small><strong>File:</strong> ' + response.data.file_path + '</small></p>';
                    } else {
                        html += '<p style="color: #666; font-style: italic;">No debug content available yet.</p>';
                    }

                    html += '</div>';
                    $results.html(html);
                } else {
                    $results.html('<div class="notice notice-error"><p><strong>Error:</strong> ' + response.data.message + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                $results.html('<div class="notice notice-error"><p><strong>Error:</strong> ' + error + '</p></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('View Debug File');
            }
        });
    });
});
