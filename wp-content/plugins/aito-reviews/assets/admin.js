jQuery(document).ready(function($) {
    
    // Test connection button
    $('#aito-test-connection').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');
        
        $button.prop('disabled', true).text(aitoReviews.strings.testing);
        $results.html('<div class="notice notice-info"><p>' + aitoReviews.strings.testing + '</p></div>');
        
        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_test_connection',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    var html = '<div class="notice notice-success"><p><strong>' + aitoReviews.strings.success + '</strong> ' + response.message + '</p>';

                    // Show review methods if available (prioritized)
                    if (response.review_methods && response.review_methods.length > 0) {
                        html += '<div style="margin: 15px 0;">';
                        html += '<h4>🎯 Available Review Methods (' + response.review_count + '):</h4>';
                        html += '<div style="margin-top: 8px;">';
                        response.review_methods.forEach(function(method) {
                            html += '<code style="display: inline-block; margin: 2px 4px; padding: 4px 8px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 3px; font-size: 12px;">' + method + '</code>';
                        });
                        html += '</div>';
                        html += '</div>';
                    }

                    // Show all feedback methods if available
                    if (response.feedback_methods && response.feedback_methods.length > 0) {
                        html += '<div style="margin: 15px 0;">';
                        html += '<h4>📊 All Feedback Methods (' + response.feedback_count + '):</h4>';
                        html += '<div style="margin-top: 8px;">';
                        response.feedback_methods.forEach(function(method) {
                            html += '<code style="display: inline-block; margin: 2px 4px; padding: 3px 6px; background: #fff2e7; border: 1px solid #ffd6b3; border-radius: 3px; font-size: 11px;">' + method + '</code>';
                        });
                        html += '</div>';
                        html += '</div>';
                    }

                    // Show extracted method names (collapsed by default)
                    if (response.method_names && response.method_names.length > 0) {
                        html += '<div style="margin: 15px 0;">';
                        html += '<h4>🔧 All SOAP Methods (' + response.total_method_names + ') <button type="button" class="button-link aito-toggle-methods" style="font-size: 12px;">Show/Hide</button></h4>';
                        html += '<div class="aito-all-methods" style="display: none; max-height: 150px; overflow-y: auto; background: #f9f9f9; padding: 10px; border: 1px solid #ddd; margin: 8px 0; border-radius: 3px;">';
                        response.method_names.forEach(function(method) {
                            html += '<code style="display: inline-block; margin: 1px 2px; padding: 1px 4px; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 2px; font-size: 10px; max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">' + method + '</code>';
                        });
                        html += '</div>';
                        html += '</div>';
                    }

                    // Show total methods count
                    if (response.total_methods) {
                        html += '<p><strong>Total SOAP Functions:</strong> ' + response.total_methods + ' | <strong>Extracted Methods:</strong> ' + response.total_method_names + '</p>';
                    }

                    html += '</div>';
                    $results.html(html);
                } else {
                    $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + response.message + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('Test Connection');
            }
        });
    });
    
    // Fetch data now button
    $('#aito-fetch-now').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        $button.prop('disabled', true).text(aitoReviews.strings.fetching);
        $results.html('<div class="notice notice-info"><p>' + aitoReviews.strings.fetching + '</p></div>');

        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_fetch_now',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    var message = '<strong>' + aitoReviews.strings.success + '</strong> ' + response.data.message;
                    if (response.data.score !== undefined && response.data.total !== undefined) {
                        message += '<br><strong>Score:</strong> ' + response.data.score + '<br><strong>Total:</strong> ' + response.data.total;
                    }

                    var html = '<div class="notice notice-success"><p>' + message + '</p></div>';

                    // Add debug information if available
                    if (response.data.debug) {
                        html += formatDebugInfo(response.data.debug, 'success');
                    }

                    $results.html(html);

                    // Refresh the page to show updated data
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                } else {
                    var html = '<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + response.data.message + '</p></div>';

                    // Add debug information if available
                    if (response.data.debug) {
                        html += formatDebugInfo(response.data.debug, 'error');
                    }

                    $results.html(html);
                }
            },
            error: function(xhr, status, error) {
                var html = '<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>';

                // Try to parse response for debug info
                try {
                    var errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.data && errorResponse.data.debug) {
                        html += formatDebugInfo(errorResponse.data.debug, 'error');
                    }
                } catch (e) {
                    // Ignore JSON parse errors
                }

                $results.html(html);
            },
            complete: function() {
                $button.prop('disabled', false).text('Fetch Data Now');
            }
        });
    });

    // Function to format debug information
    function formatDebugInfo(debug, type) {
        var html = '<div class="aito-debug-info ' + type + '">';
        html += '<h4>Debug Information <button type="button" class="button-link aito-toggle-debug">Show/Hide</button></h4>';
        html += '<div class="aito-debug-content" style="display: none;">';

        // Timestamp
        if (debug.timestamp) {
            html += '<p><strong>Timestamp:</strong> ' + debug.timestamp + '</p>';
        }

        // Settings
        if (debug.settings) {
            html += '<h5>Settings:</h5>';
            html += '<ul>';
            for (var key in debug.settings) {
                html += '<li><strong>' + key + ':</strong> ' + debug.settings[key] + '</li>';
            }
            html += '</ul>';
        }

        // Connection test
        if (debug.connection_test) {
            html += '<h5>Connection Test:</h5>';
            html += '<p><strong>Success:</strong> ' + (debug.connection_test.success ? 'Yes' : 'No') + '</p>';
            html += '<p><strong>Message:</strong> ' + debug.connection_test.message + '</p>';
        }

        // Fetch timing
        if (debug.fetch_duration) {
            html += '<p><strong>Fetch Duration:</strong> ' + debug.fetch_duration + '</p>';
        }

        // Data
        if (debug.data) {
            html += '<h5>Retrieved Data:</h5>';
            html += '<pre>' + JSON.stringify(debug.data, null, 2) + '</pre>';
        }

        // Storage info
        if (debug.storage) {
            html += '<h5>Storage Information:</h5>';
            html += '<pre>' + JSON.stringify(debug.storage, null, 2) + '</pre>';
        }

        // Error details
        if (debug.error) {
            html += '<h5>Error Details:</h5>';
            html += '<p class="error-message">' + debug.error + '</p>';
        }

        // Exception details
        if (debug.exception) {
            html += '<h5>Exception Details:</h5>';
            html += '<p><strong>Message:</strong> ' + debug.exception.message + '</p>';
            html += '<p><strong>File:</strong> ' + debug.exception.file + ':' + debug.exception.line + '</p>';
            if (debug.exception.trace) {
                html += '<h6>Stack Trace:</h6>';
                html += '<pre class="stack-trace">' + debug.exception.trace + '</pre>';
            }
        }

        html += '</div>';
        html += '</div>';

        return html;
    }

    // Toggle debug info visibility
    $(document).on('click', '.aito-toggle-debug', function() {
        $(this).closest('.aito-debug-info').find('.aito-debug-content').toggle();
    });

    // Toggle methods list visibility
    $(document).on('click', '.aito-toggle-methods', function() {
        $(this).closest('div').find('.aito-all-methods').toggle();
    });

    // Clear debug log button
    $('#aito-clear-debug').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        if (!confirm('Are you sure you want to clear the debug log?')) {
            return;
        }

        $button.prop('disabled', true).text('Clearing...');

        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_clear_debug',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    $results.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');

                    // Hide debug log section
                    $('.aito-debug-log-section').fadeOut();

                    // Refresh after 2 seconds
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + response.data.message + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('Clear Debug Log');
            }
        });
    });
    
    // Auto-save settings when changed
    $('input[name="aito_reviews_enabled"]').on('change', function() {
        var $checkbox = $(this);
        var isEnabled = $checkbox.is(':checked');
        
        // Show/hide cron interval field based on enabled status
        var $intervalRow = $('select[name="aito_reviews_cron_interval"]').closest('tr');
        if (isEnabled) {
            $intervalRow.show();
        } else {
            $intervalRow.hide();
        }
    });
    
    // Initialize visibility on page load
    var isEnabled = $('input[name="aito_reviews_enabled"]').is(':checked');
    var $intervalRow = $('select[name="aito_reviews_cron_interval"]').closest('tr');
    if (!isEnabled) {
        $intervalRow.hide();
    }
    
    // Form validation
    $('form').on('submit', function(e) {
        var endpointUrl = $('input[name="aito_reviews_endpoint_url"]').val();
        
        if (!endpointUrl) {
            alert('Please enter an API endpoint URL.');
            e.preventDefault();
            return false;
        }
        
        // Basic URL validation
        try {
            new URL(endpointUrl);
        } catch (error) {
            alert('Please enter a valid URL for the API endpoint.');
            e.preventDefault();
            return false;
        }
    });
    
    // Add tooltips for help text
    $('.description').each(function() {
        var $desc = $(this);
        var $input = $desc.prev('input, select, textarea');
        
        if ($input.length) {
            $input.attr('title', $desc.text());
        }
    });
    
    // Status refresh functionality
    if ($('.aito-status-info').length) {
        setInterval(function() {
            // Auto-refresh status every 30 seconds if on the settings page
            if (document.visibilityState === 'visible') {
                // Only refresh if no AJAX is currently running
                if (!$('#aito-test-connection').prop('disabled') && !$('#aito-fetch-now').prop('disabled')) {
                    // Could add auto-refresh logic here if needed
                }
            }
        }, 30000);
    }

    // Test authentication button
    $('#aito-test-auth').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        $button.prop('disabled', true).text('Testing Authentication...');
        $results.html('<div class="notice notice-info"><p>Testing authentication methods...</p></div>');

        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_test_auth',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    var html = '<div class="notice notice-success"><p><strong>✅ Authentication Successful!</strong> ' + response.data.message + '</p>';

                    if (response.data.data) {
                        html += '<h4>Retrieved Data:</h4>';
                        html += '<pre style="background: #f0f8ff; padding: 10px; border: 1px solid #0073aa; border-radius: 3px; max-height: 200px; overflow-y: auto;">';
                        html += JSON.stringify(response.data.data, null, 2);
                        html += '</pre>';
                    }

                    html += '</div>';
                    $results.html(html);
                } else {
                    var html = '<div class="notice notice-error"><p><strong>❌ Authentication Failed:</strong> ' + response.data.message + '</p>';

                    if (response.data.error) {
                        html += '<h4>Error Details:</h4>';
                        html += '<p style="background: #fff2f2; padding: 8px; border: 1px solid #d63638; border-radius: 3px;">' + response.data.error + '</p>';
                    }

                    if (response.data.debug_log && response.data.debug_log.length > 0) {
                        html += '<h4>Recent Debug Log:</h4>';
                        html += '<div style="background: #f9f9f9; padding: 8px; border: 1px solid #ddd; border-radius: 3px; max-height: 150px; overflow-y: auto;">';
                        response.data.debug_log.forEach(function(entry) {
                            html += '<div style="font-family: monospace; font-size: 11px; margin: 2px 0;">' + entry + '</div>';
                        });
                        html += '</div>';
                    }

                    if (response.data.exception) {
                        html += '<h4>Exception Details:</h4>';
                        html += '<p><strong>Message:</strong> ' + response.data.exception.message + '</p>';
                        html += '<p><strong>Location:</strong> ' + response.data.exception.file + ':' + response.data.exception.line + '</p>';
                    }

                    html += '</div>';
                    $results.html(html);
                }
            },
            error: function(xhr, status, error) {
                $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('Test Authentication');
            }
        });
    });

    // Test method authentication requirements button
    $('#aito-test-methods').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        $button.prop('disabled', true).text('Testing Methods...');
        $results.html('<div class="notice notice-info"><p>Testing authentication requirements for all feedback methods...</p></div>');

        $.ajax({
            url: aitoReviews.ajaxUrl,
            type: 'POST',
            data: {
                action: 'aito_test_methods',
                nonce: aitoReviews.nonce
            },
            success: function(response) {
                if (response.success) {
                    var html = '<div class="notice notice-success"><p><strong>✅ Method Testing Complete!</strong> ' + response.data.message + '</p>';

                    // Summary
                    html += '<div style="background: #f0f8ff; padding: 15px; border: 1px solid #0073aa; border-radius: 4px; margin: 15px 0;">';
                    html += '<h4>📊 Summary:</h4>';
                    html += '<ul>';
                    html += '<li><strong>🟢 Working without auth:</strong> ' + response.data.summary.no_auth_count + ' methods</li>';
                    html += '<li><strong>🔐 Requiring auth:</strong> ' + response.data.summary.auth_required_count + ' methods</li>';
                    html += '<li><strong>❓ Unknown status:</strong> ' + response.data.summary.unknown_count + ' methods</li>';
                    html += '</ul>';
                    html += '</div>';

                    // Working methods (no auth required)
                    if (response.data.categorized.working_methods.length > 0) {
                        html += '<div style="background: #d4edda; padding: 12px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;">';
                        html += '<h4>🟢 Methods Working WITHOUT Authentication:</h4>';
                        response.data.categorized.working_methods.forEach(function(method) {
                            html += '<div style="margin: 8px 0; padding: 8px; background: #fff; border-radius: 3px;">';
                            html += '<strong><code>' + method.method + '</code></strong> - ' + method.message;
                            if (method.response_preview) {
                                html += '<br><small style="color: #666;">Response preview: ' + method.response_preview.substring(0, 100) + '...</small>';
                            }
                            html += '</div>';
                        });
                        html += '</div>';
                    }

                    // Auth required methods
                    if (response.data.categorized.auth_required_methods.length > 0) {
                        html += '<div style="background: #fff3cd; padding: 12px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0;">';
                        html += '<h4>🔐 Methods Requiring Authentication:</h4>';
                        response.data.categorized.auth_required_methods.forEach(function(method) {
                            html += '<div style="margin: 8px 0; padding: 8px; background: #fff; border-radius: 3px;">';
                            html += '<strong><code>' + method.method + '</code></strong> - ' + method.message;
                            html += '</div>';
                        });
                        html += '</div>';
                    }

                    // Unknown methods
                    if (response.data.categorized.unknown_methods.length > 0) {
                        html += '<div style="background: #f8d7da; padding: 12px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;">';
                        html += '<h4>❓ Methods with Unknown Status:</h4>';
                        response.data.categorized.unknown_methods.forEach(function(method) {
                            html += '<div style="margin: 8px 0; padding: 8px; background: #fff; border-radius: 3px;">';
                            html += '<strong><code>' + method.method + '</code></strong> - ' + method.message;
                            html += '</div>';
                        });
                        html += '</div>';
                    }

                    html += '</div>';
                    $results.html(html);
                } else {
                    var html = '<div class="notice notice-error"><p><strong>❌ Method Testing Failed:</strong> ' + response.data.message + '</p>';

                    if (response.data.exception) {
                        html += '<h4>Exception Details:</h4>';
                        html += '<p><strong>Message:</strong> ' + response.data.exception.message + '</p>';
                        html += '<p><strong>Location:</strong> ' + response.data.exception.file + ':' + response.data.exception.line + '</p>';
                    }

                    html += '</div>';
                    $results.html(html);
                }
            },
            error: function(xhr, status, error) {
                $results.html('<div class="notice notice-error"><p><strong>' + aitoReviews.strings.error + '</strong> ' + error + '</p></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('Test Method Auth Requirements');
            }
        });
    });

    // Inspect WSDL button
    $('#aito-inspect-wsdl').on('click', function() {
        var $button = $(this);
        var $results = $('#aito-action-results');

        $button.prop('disabled', true).text('Inspecting WSDL...');
        $results.html('<div class="notice notice-info"><p>Inspecting WSDL signatures...</p></div>');

        $.post(aitoReviews.ajaxUrl, {
            action: 'aito_inspect_wsdl',
            nonce: aitoReviews.nonce
        }, function(response) {
            $button.prop('disabled', false).text('Inspect WSDL Signatures');

            if (response.success) {
                var html = '<div class="notice notice-success"><p><strong>🔍 WSDL Inspection Results:</strong></p>';
                html += '<p>' + response.data.message + '</p>';
                html += '<p><strong>Endpoint:</strong> <code>' + response.data.endpoint + '</code></p>';

                if (response.data.relevant_functions && response.data.relevant_functions.length > 0) {
                    html += '<div style="background: #f0f8ff; padding: 12px; border: 1px solid #0073aa; border-radius: 4px; margin: 10px 0;">';
                    html += '<h4>🎯 Relevant Functions (' + response.data.relevant_functions.length + '):</h4>';
                    response.data.relevant_functions.forEach(function(func) {
                        html += '<div style="margin: 4px 0; padding: 6px; background: #fff; border-radius: 3px; font-family: monospace; font-size: 12px; border-left: 3px solid #0073aa;">' + func + '</div>';
                    });
                    html += '</div>';
                }

                if (response.data.relevant_types && response.data.relevant_types.length > 0) {
                    html += '<div style="background: #fff3cd; padding: 12px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0;">';
                    html += '<h4>📋 Relevant Types (' + response.data.relevant_types.length + '):</h4>';
                    response.data.relevant_types.forEach(function(type) {
                        html += '<div style="margin: 6px 0; padding: 8px; background: #fff; border-radius: 3px; border-left: 3px solid #ffc107;"><pre style="margin: 0; font-size: 11px; white-space: pre-wrap;">' + type + '</pre></div>';
                    });
                    html += '</div>';
                }

                // Add collapsible section for all functions
                if (response.data.all_functions && response.data.all_functions.length > 0) {
                    html += '<div style="margin: 15px 0;">';
                    html += '<h4>🔧 All SOAP Functions (' + response.data.all_functions.length + ') <button type="button" class="button-link aito-toggle-all-functions" style="font-size: 12px;">Show/Hide</button></h4>';
                    html += '<div class="aito-all-functions" style="display: none; max-height: 300px; overflow-y: auto; background: #f9f9f9; padding: 10px; border: 1px solid #ddd; margin: 8px 0; border-radius: 3px;">';
                    response.data.all_functions.forEach(function(func) {
                        html += '<div style="margin: 2px 0; padding: 4px; background: #fff; border-radius: 2px; font-family: monospace; font-size: 10px; border-left: 2px solid #ccc;">' + func + '</div>';
                    });
                    html += '</div>';
                    html += '</div>';
                }

                html += '</div>';
                $results.html(html);
            } else {
                $results.html('<div class="notice notice-error"><p><strong>Error:</strong> ' + response.data.message + '</p></div>');
            }
        }).fail(function() {
            $button.prop('disabled', false).text('Inspect WSDL Signatures');
            $results.html('<div class="notice notice-error"><p><strong>Error:</strong> Failed to inspect WSDL</p></div>');
        });
    });

    // Toggle all functions visibility
    $(document).on('click', '.aito-toggle-all-functions', function() {
        $(this).closest('div').find('.aito-all-functions').toggle();
    });
});
