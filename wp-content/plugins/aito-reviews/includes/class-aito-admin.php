<?php
/**
 * AITO Reviews Admin Class
 * 
 * Handles admin interface and settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AITO_Reviews_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
        add_action('wp_ajax_aito_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_aito_fetch_now', array($this, 'ajax_fetch_now'));
        add_action('wp_ajax_aito_clear_debug', array($this, 'ajax_clear_debug'));
        add_action('wp_ajax_aito_test_auth', array($this, 'ajax_test_auth'));
        add_action('wp_ajax_aito_test_methods', array($this, 'ajax_test_methods'));
        add_action('wp_ajax_aito_inspect_wsdl', array($this, 'ajax_inspect_wsdl'));
        add_action('wp_ajax_aito_clear_session', array($this, 'ajax_clear_session'));
        add_action('wp_ajax_aito_test_stdclass', array($this, 'ajax_test_stdclass'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('AITO Reviews Settings', 'aito-reviews'),
            __('AITO Reviews', 'aito-reviews'),
            'manage_options',
            'aito-reviews',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Initialize settings
     */
    public function init_settings() {
        // Register settings
        register_setting('aito_reviews_settings', 'aito_reviews_endpoint_url');
        register_setting('aito_reviews_settings', 'aito_reviews_username');
        register_setting('aito_reviews_settings', 'aito_reviews_password');
        register_setting('aito_reviews_settings', 'aito_reviews_company_id');
        register_setting('aito_reviews_settings', 'aito_reviews_method');
        register_setting('aito_reviews_settings', 'aito_reviews_cron_interval');
        register_setting('aito_reviews_settings', 'aito_reviews_enabled');
        
        // Add settings sections
        add_settings_section(
            'aito_reviews_api_section',
            __('API Configuration', 'aito-reviews'),
            array($this, 'api_section_callback'),
            'aito_reviews_settings'
        );
        
        add_settings_section(
            'aito_reviews_cron_section',
            __('Cron Settings', 'aito-reviews'),
            array($this, 'cron_section_callback'),
            'aito_reviews_settings'
        );
        
        add_settings_section(
            'aito_reviews_status_section',
            __('Status & Data', 'aito-reviews'),
            array($this, 'status_section_callback'),
            'aito_reviews_settings'
        );

        add_settings_section(
            'aito_reviews_system_section',
            __('System Information', 'aito-reviews'),
            array($this, 'system_section_callback'),
            'aito_reviews_settings'
        );
        
        // Add settings fields
        add_settings_field(
            'aito_reviews_endpoint_url',
            __('API Endpoint URL', 'aito-reviews'),
            array($this, 'endpoint_url_field'),
            'aito_reviews_settings',
            'aito_reviews_api_section'
        );
        
        add_settings_field(
            'aito_reviews_username',
            __('Username', 'aito-reviews'),
            array($this, 'username_field'),
            'aito_reviews_settings',
            'aito_reviews_api_section'
        );
        
        add_settings_field(
            'aito_reviews_password',
            __('Password', 'aito-reviews'),
            array($this, 'password_field'),
            'aito_reviews_settings',
            'aito_reviews_api_section'
        );
        
        add_settings_field(
            'aito_reviews_company_id',
            __('Company ID', 'aito-reviews'),
            array($this, 'company_id_field'),
            'aito_reviews_settings',
            'aito_reviews_api_section'
        );

        add_settings_field(
            'aito_reviews_method',
            __('API Method', 'aito-reviews'),
            array($this, 'method_field'),
            'aito_reviews_settings',
            'aito_reviews_api_section'
        );
        
        add_settings_field(
            'aito_reviews_enabled',
            __('Enable Automatic Updates', 'aito-reviews'),
            array($this, 'enabled_field'),
            'aito_reviews_settings',
            'aito_reviews_cron_section'
        );
        
        add_settings_field(
            'aito_reviews_cron_interval',
            __('Update Interval', 'aito-reviews'),
            array($this, 'cron_interval_field'),
            'aito_reviews_settings',
            'aito_reviews_cron_section'
        );
    }
    
    /**
     * Admin page
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <?php settings_errors(); ?>
            
            <form method="post" action="options.php">
                <?php
                settings_fields('aito_reviews_settings');
                do_settings_sections('aito_reviews_settings');
                submit_button();
                ?>
            </form>
            
            <div class="aito-reviews-actions">
                <h3><?php _e('Actions', 'aito-reviews'); ?></h3>
                <p>
                    <button type="button" id="aito-test-connection" class="button button-secondary">
                        <?php _e('Test Connection', 'aito-reviews'); ?>
                    </button>
                    <button type="button" id="aito-fetch-now" class="button button-secondary">
                        <?php _e('Fetch Data Now', 'aito-reviews'); ?>
                    </button>
                    <button type="button" id="aito-clear-debug" class="button button-secondary">
                        <?php _e('Clear Debug Log', 'aito-reviews'); ?>
                    </button>
                    <button type="button" id="aito-test-auth" class="button button-secondary">
                        <?php _e('Test Authentication', 'aito-reviews'); ?>
                    </button>
                    <button type="button" id="aito-test-methods" class="button button-secondary">
                        <?php _e('Test Method Auth Requirements', 'aito-reviews'); ?>
                    </button>
                    <button type="button" id="aito-inspect-wsdl" class="button button-secondary">
                        <?php _e('Inspect WSDL Signatures', 'aito-reviews'); ?>
                    </button>
                    <button type="button" id="aito-test-credentials" class="button button-secondary">
                        <?php _e('Test Credentials Only', 'aito-reviews'); ?>
                    </button>
                    <button type="button" id="aito-clear-debug" class="button button-secondary">
                        <?php _e('Clear Debug Log', 'aito-reviews'); ?>
                    </button>
                    <button type="button" id="aito-test-stdclass" class="button button-secondary">
                        <?php _e('Test stdClass Fix', 'aito-reviews'); ?>
                    </button>
                </p>
                <div id="aito-action-results"></div>
            </div>

            <?php $this->display_debug_log(); ?>
        </div>
        <?php
    }
    
    /**
     * Section callbacks
     */
    public function api_section_callback() {
        echo '<p>' . __('Configure your AITO API connection settings.', 'aito-reviews') . '</p>';
    }
    
    public function cron_section_callback() {
        echo '<p>' . __('Configure automatic data fetching schedule.', 'aito-reviews') . '</p>';
    }
    
    public function status_section_callback() {
        $last_update = get_option('aito_reviews_last_update', 0);
        $last_error = get_option('aito_reviews_last_error', '');
        $score = get_option('aito_reviews_score', 0);
        $total = get_option('aito_reviews_total', 0);

        // Get session status
        $soap_client = new AITO_SOAP_Client();
        $session_status = $soap_client->get_session_status();

        echo '<div class="aito-status-info">';
        echo '<h4>' . __('Current Data', 'aito-reviews') . '</h4>';
        echo '<p><strong>' . __('Score:', 'aito-reviews') . '</strong> ' . esc_html($score) . '</p>';
        echo '<p><strong>' . __('Total Reviews:', 'aito-reviews') . '</strong> ' . esc_html($total) . '</p>';

        if ($last_update) {
            echo '<p><strong>' . __('Last Updated:', 'aito-reviews') . '</strong> ' . esc_html(date('Y-m-d H:i:s', $last_update)) . '</p>';
        } else {
            echo '<p><strong>' . __('Last Updated:', 'aito-reviews') . '</strong> ' . __('Never', 'aito-reviews') . '</p>';
        }

        // Session status
        echo '<h4>' . __('Session Status', 'aito-reviews') . '</h4>';
        if ($session_status['has_session']) {
            echo '<p><strong style="color: green;">✓ ' . esc_html($session_status['message']) . '</strong></p>';
            if (isset($session_status['authenticated_at'])) {
                echo '<p><strong>' . __('Authenticated At:', 'aito-reviews') . '</strong> ' . esc_html($session_status['authenticated_at']) . '</p>';
            }
            if (isset($session_status['expires_in'])) {
                echo '<p><strong>' . __('Expires In:', 'aito-reviews') . '</strong> ' . esc_html($session_status['expires_in']) . '</p>';
            }
            if (isset($session_status['username'])) {
                echo '<p><strong>' . __('Username:', 'aito-reviews') . '</strong> ' . esc_html($session_status['username']) . '</p>';
            }
            if (isset($session_status['auth_method'])) {
                echo '<p><strong>' . __('Auth Method:', 'aito-reviews') . '</strong> ' . esc_html($session_status['auth_method']) . '</p>';
            }
            if (isset($session_status['cookie_count']) && $session_status['cookie_count'] > 0) {
                echo '<p><strong>' . __('Session Cookies:', 'aito-reviews') . '</strong> ' . esc_html($session_status['cookie_count']) . '</p>';
            }
            echo '<p><em>' . __('Next API call will use getFeedbackRating (no auth required)', 'aito-reviews') . '</em></p>';
            echo '<p><button type="button" id="aito-clear-session" class="button button-secondary" style="color: #d63638;">Clear Session</button></p>';
        } else {
            echo '<p><strong style="color: orange;">⚠ ' . esc_html($session_status['message']) . '</strong></p>';
            if (isset($session_status['expired_at'])) {
                echo '<p><strong>' . __('Expired At:', 'aito-reviews') . '</strong> ' . esc_html($session_status['expired_at']) . '</p>';
            }
            echo '<p><em>' . __('Next API call will attempt login method first, then fallback to getFeedbackRatingA', 'aito-reviews') . '</em></p>';
        }

        if (!empty($last_error)) {
            echo '<h4>' . __('Last Error', 'aito-reviews') . '</h4>';
            echo '<p><strong style="color: red;">' . __('Error Details:', 'aito-reviews') . '</strong> ';
            echo '<button type="button" class="button-link aito-toggle-error" style="color: red; text-decoration: underline; font-size: 12px; margin-left: 5px;">Show/Hide</button></p>';
            echo '<div class="aito-error-details" style="display: none; background: #fff2f2; padding: 10px; border: 1px solid #d63638; border-radius: 3px; margin: 8px 0; max-height: 200px; overflow-y: auto;">';
            echo '<pre style="margin: 0; white-space: pre-wrap; font-size: 11px; color: #d63638;">' . esc_html($last_error) . '</pre>';
            echo '</div>';
        }
        echo '</div>';
    }

    public function system_section_callback() {
        $plugin = AITO_Reviews_Plugin::get_instance();
        $system_info = $plugin->get_system_info();

        echo '<div class="aito-system-info">';
        echo '<h4>' . __('System Requirements', 'aito-reviews') . '</h4>';
        echo '<table class="widefat">';
        echo '<thead><tr><th>' . __('Requirement', 'aito-reviews') . '</th><th>' . __('Status', 'aito-reviews') . '</th></tr></thead>';
        echo '<tbody>';

        // PHP Version
        $php_ok = version_compare($system_info['php_version'], '7.0', '>=');
        echo '<tr>';
        echo '<td>PHP 7.0+</td>';
        echo '<td><span class="' . ($php_ok ? 'aito-success' : 'aito-error') . '">';
        echo $php_ok ? '✓ ' . $system_info['php_version'] : '✗ ' . $system_info['php_version'] . ' (upgrade required)';
        echo '</span></td>';
        echo '</tr>';

        // Extensions
        $extensions = array(
            'soap_enabled' => 'SOAP Extension',
            'openssl_enabled' => 'OpenSSL Extension',
            'curl_enabled' => 'cURL Extension',
            'libxml_enabled' => 'LibXML Extension'
        );

        foreach ($extensions as $key => $name) {
            echo '<tr>';
            echo '<td>' . esc_html($name) . '</td>';
            echo '<td><span class="' . ($system_info[$key] ? 'aito-success' : 'aito-error') . '">';
            echo $system_info[$key] ? '✓ Enabled' : '✗ Not Available';
            echo '</span></td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';

        if (!$system_info['soap_enabled']) {
            echo '<div class="notice notice-error inline">';
            echo '<p><strong>' . __('SOAP Extension Required:', 'aito-reviews') . '</strong> ';
            echo __('The PHP SOAP extension is required for this plugin to work. Please contact your hosting provider to enable it.', 'aito-reviews');
            echo '</p></div>';
        }

        echo '</div>';
    }

    /**
     * Field callbacks
     */
    public function endpoint_url_field() {
        $value = get_option('aito_reviews_endpoint_url', 'https://www.aito.com/api/v1/api.asmx');
        echo '<input type="url" name="aito_reviews_endpoint_url" value="' . esc_attr($value) . '" class="regular-text" required />';
        echo '<p class="description">' . __('The AITO SOAP API endpoint URL.', 'aito-reviews') . '</p>';
    }
    
    public function username_field() {
        $value = get_option('aito_reviews_username', '');
        echo '<input type="text" name="aito_reviews_username" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Username for API authentication (if required).', 'aito-reviews') . '</p>';
    }
    
    public function password_field() {
        $value = get_option('aito_reviews_password', '');
        echo '<input type="password" name="aito_reviews_password" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Password for API authentication (if required).', 'aito-reviews') . '</p>';
    }
    
    public function company_id_field() {
        $value = get_option('aito_reviews_company_id', '');
        echo '<input type="text" name="aito_reviews_company_id" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Company ID parameter for the API call (if required).', 'aito-reviews') . '</p>';
    }

    public function method_field() {
        $value = get_option('aito_reviews_method', 'getFeedbackRatingA');

        // Common AITO review methods
        $methods = array(
            'getFeedbackRatingA' => 'getFeedbackRatingA (Method A)',
            'getFeedbackRatingB' => 'getFeedbackRatingB (Method B)',
            'getFeedbackRating' => 'getFeedbackRating (Generic)',
            'getReviewData' => 'getReviewData',
            'getCustomerFeedback' => 'getCustomerFeedback'
        );

        echo '<select name="aito_reviews_method" id="aito_reviews_method">';
        foreach ($methods as $method_key => $method_label) {
            echo '<option value="' . esc_attr($method_key) . '" ' . selected($method_key, $value, false) . '>' . esc_html($method_label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('Select the AITO API method to use for fetching review data. Use "Test Connection" to see available methods.', 'aito-reviews') . '</p>';

        // Show detected methods if available
        $soap_client = new AITO_SOAP_Client();
        $connection_test = $soap_client->test_connection();
        if ($connection_test['success']) {
            echo '<div class="aito-detected-methods" style="margin-top: 10px; padding: 12px; background: #f0f8ff; border: 1px solid #0073aa; border-radius: 4px; max-width: 100%;">';

            if (!empty($connection_test['review_methods'])) {
                echo '<div style="margin-bottom: 12px;">';
                echo '<strong>' . __('Detected Review Methods:', 'aito-reviews') . '</strong><br>';
                echo '<div style="margin-top: 6px;">';
                foreach ($connection_test['review_methods'] as $detected_method) {
                    echo '<code>' . esc_html($detected_method) . '</code>';
                }
                echo '</div>';
                echo '</div>';
            }

            if (!empty($connection_test['method_names'])) {
                echo '<div>';
                echo '<strong>' . __('All Available Methods:', 'aito-reviews') . '</strong>';
                echo '<div class="aito-method-list">';
                foreach ($connection_test['method_names'] as $method_name) {
                    echo '<code>' . esc_html($method_name) . '</code>';
                }
                echo '</div>';
                echo '</div>';
            }

            echo '</div>';
        }
    }
    
    public function enabled_field() {
        $value = get_option('aito_reviews_enabled', false);
        echo '<input type="checkbox" name="aito_reviews_enabled" value="1" ' . checked(1, $value, false) . ' />';
        echo '<label for="aito_reviews_enabled">' . __('Enable automatic data fetching via WordPress cron', 'aito-reviews') . '</label>';
    }
    
    public function cron_interval_field() {
        $value = get_option('aito_reviews_cron_interval', 'hourly');
        $intervals = array(
            'fifteen_minutes' => __('Every 15 Minutes', 'aito-reviews'),
            'thirty_minutes' => __('Every 30 Minutes', 'aito-reviews'),
            'hourly' => __('Hourly', 'aito-reviews'),
            'two_hours' => __('Every 2 Hours', 'aito-reviews'),
            'six_hours' => __('Every 6 Hours', 'aito-reviews'),
            'twicedaily' => __('Twice Daily', 'aito-reviews'),
            'daily' => __('Daily', 'aito-reviews')
        );
        
        echo '<select name="aito_reviews_cron_interval">';
        foreach ($intervals as $key => $label) {
            echo '<option value="' . esc_attr($key) . '" ' . selected($key, $value, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('How often to fetch data from the AITO API.', 'aito-reviews') . '</p>';
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'settings_page_aito-reviews') {
            return;
        }

        wp_enqueue_script(
            'aito-reviews-admin',
            AITO_REVIEWS_PLUGIN_URL . 'assets/admin.js',
            array('jquery'),
            AITO_REVIEWS_VERSION,
            true
        );

        wp_enqueue_style(
            'aito-reviews-admin',
            AITO_REVIEWS_PLUGIN_URL . 'assets/style.css',
            array(),
            AITO_REVIEWS_VERSION
        );

        wp_localize_script('aito-reviews-admin', 'aitoReviews', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('aito_reviews_nonce'),
            'strings' => array(
                'testing' => __('Testing connection...', 'aito-reviews'),
                'fetching' => __('Fetching data...', 'aito-reviews'),
                'success' => __('Success!', 'aito-reviews'),
                'error' => __('Error:', 'aito-reviews')
            )
        ));
    }
    
    /**
     * AJAX test connection
     */
    public function ajax_test_connection() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }
        
        $soap_client = new AITO_SOAP_Client();
        $result = $soap_client->test_connection();
        
        wp_send_json($result);
    }
    
    /**
     * AJAX fetch data now
     */
    public function ajax_fetch_now() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        // Enable debug mode for this request
        $debug_info = array();
        $debug_info['timestamp'] = current_time('mysql');
        $debug_info['settings'] = array(
            'endpoint_url' => get_option('aito_reviews_endpoint_url', ''),
            'username' => !empty(get_option('aito_reviews_username', '')) ? '[SET]' : '[EMPTY]',
            'password' => !empty(get_option('aito_reviews_password', '')) ? '[SET]' : '[EMPTY]',
            'company_id' => get_option('aito_reviews_company_id', ''),
            'enabled' => get_option('aito_reviews_enabled', false)
        );

        // Initialize SOAP client with debug
        $soap_client = new AITO_SOAP_Client();

        try {
            // Test connection first
            $debug_info['connection_test'] = $soap_client->test_connection();

            // Attempt to fetch data
            $debug_info['fetch_start'] = microtime(true);
            $data = $soap_client->get_feedback_rating();
            $debug_info['fetch_end'] = microtime(true);
            $debug_info['fetch_duration'] = round(($debug_info['fetch_end'] - $debug_info['fetch_start']) * 1000, 2) . 'ms';

            if ($data !== false) {
                // Store data
                $cron = new AITO_Reviews_Cron();
                $store_result = $cron->store_data_debug($data);

                $debug_info['success'] = true;
                $debug_info['data'] = $data;
                $debug_info['storage'] = $store_result;

                wp_send_json_success(array(
                    'message' => __('Data fetched successfully!', 'aito-reviews'),
                    'score' => $data['score'],
                    'total' => $data['total'],
                    'debug' => $debug_info
                ));
            } else {
                $debug_info['success'] = false;
                $debug_info['error'] = get_option('aito_reviews_last_error', 'Unknown error');
                $debug_info['raw_response'] = get_option('aito_reviews_raw_response', null);

                wp_send_json_error(array(
                    'message' => __('Failed to fetch data from AITO API', 'aito-reviews'),
                    'debug' => $debug_info
                ));
            }

        } catch (Exception $e) {
            $debug_info['success'] = false;
            $debug_info['exception'] = array(
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            );

            wp_send_json_error(array(
                'message' => __('Exception occurred: ', 'aito-reviews') . $e->getMessage(),
                'debug' => $debug_info
            ));
        }
    }

    /**
     * Display debug log
     */
    private function display_debug_log() {
        $soap_client = new AITO_SOAP_Client();
        $debug_log = $soap_client->get_debug_log();

        if (empty($debug_log)) {
            return;
        }

        echo '<div class="aito-debug-log-section">';
        echo '<h3>' . __('Debug Log', 'aito-reviews') . '</h3>';
        echo '<div class="aito-debug-log">';
        echo '<div class="debug-log-content" style="max-height: 300px; overflow-y: auto; background: #f9f9f9; padding: 10px; border: 1px solid #ddd;">';

        foreach (array_reverse($debug_log) as $entry) {
            echo '<div class="debug-entry">' . esc_html($entry) . '</div>';
        }

        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    /**
     * AJAX clear debug log
     */
    public function ajax_clear_debug() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        $soap_client = new AITO_SOAP_Client();
        $result = $soap_client->clear_debug_log();

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Debug log cleared successfully!', 'aito-reviews')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to clear debug log.', 'aito-reviews')
            ));
        }
    }

    /**
     * AJAX test authentication
     */
    public function ajax_test_auth() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        $soap_client = new AITO_SOAP_Client();

        try {
            // Try to call a simple method to test authentication
            $method = get_option('aito_reviews_method', 'getFeedbackRating');
            $response = $soap_client->get_feedback_rating($method);

            if ($response !== false) {
                wp_send_json_success(array(
                    'message' => __('Authentication successful! Data retrieved.', 'aito-reviews'),
                    'data' => $response
                ));
            } else {
                // Get debug log for detailed error info
                $debug_log = $soap_client->get_debug_log();
                $last_error = get_option('aito_reviews_last_error', 'Unknown error');

                wp_send_json_error(array(
                    'message' => __('Authentication failed or method error.', 'aito-reviews'),
                    'error' => $last_error,
                    'debug_log' => array_slice($debug_log, -5) // Last 5 entries
                ));
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Authentication test failed: ', 'aito-reviews') . $e->getMessage(),
                'exception' => array(
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }

    /**
     * AJAX test method authentication requirements
     */
    public function ajax_test_methods() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        $soap_client = new AITO_SOAP_Client();

        try {
            $results = $soap_client->test_method_auth_requirements();

            if (isset($results['error'])) {
                wp_send_json_error(array(
                    'message' => $results['error']
                ));
                return;
            }

            // Categorize results
            $no_auth_methods = array();
            $auth_required_methods = array();
            $unknown_methods = array();
            $working_methods = array();

            foreach ($results as $method => $result) {
                if ($result['status'] === 'success') {
                    $working_methods[] = array(
                        'method' => $method,
                        'requires_auth' => false,
                        'message' => $result['message'],
                        'response_preview' => $result['response_preview']
                    );
                    $no_auth_methods[] = $method;
                } elseif ($result['requires_auth'] === true) {
                    $auth_required_methods[] = array(
                        'method' => $method,
                        'message' => $result['message'],
                        'error_code' => $result['error_code'] ?? 'N/A'
                    );
                } else {
                    $unknown_methods[] = array(
                        'method' => $method,
                        'message' => $result['message'],
                        'status' => $result['status']
                    );
                }
            }

            wp_send_json_success(array(
                'message' => sprintf(
                    __('Tested %d methods. Found %d working without auth, %d requiring auth, %d unknown.', 'aito-reviews'),
                    count($results),
                    count($no_auth_methods),
                    count($auth_required_methods),
                    count($unknown_methods)
                ),
                'results' => $results,
                'summary' => array(
                    'total' => count($results),
                    'no_auth_count' => count($no_auth_methods),
                    'auth_required_count' => count($auth_required_methods),
                    'unknown_count' => count($unknown_methods),
                    'working_count' => count($working_methods)
                ),
                'categorized' => array(
                    'no_auth_methods' => $no_auth_methods,
                    'auth_required_methods' => $auth_required_methods,
                    'unknown_methods' => $unknown_methods,
                    'working_methods' => $working_methods
                )
            ));

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Method testing failed: ', 'aito-reviews') . $e->getMessage(),
                'exception' => array(
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }

    /**
     * AJAX inspect WSDL signatures
     */
    public function ajax_inspect_wsdl() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        $soap_client = new AITO_SOAP_Client();

        try {
            $signatures = $soap_client->get_method_signatures();

            if (isset($signatures['error'])) {
                wp_send_json_error(array(
                    'message' => $signatures['error']
                ));
                return;
            }

            // Filter to show only feedback/auth related methods
            $relevant_functions = array();
            $relevant_types = array();

            foreach ($signatures['functions'] as $function) {
                if (preg_match('/feedback|rating|auth|login/i', $function)) {
                    $relevant_functions[] = $function;
                }
            }

            foreach ($signatures['types'] as $type) {
                if (preg_match('/auth|feedback|rating|login/i', $type)) {
                    $relevant_types[] = $type;
                }
            }

            wp_send_json_success(array(
                'message' => sprintf(
                    __('Found %d relevant functions and %d relevant types in WSDL', 'aito-reviews'),
                    count($relevant_functions),
                    count($relevant_types)
                ),
                'endpoint' => $signatures['endpoint'],
                'relevant_functions' => $relevant_functions,
                'relevant_types' => $relevant_types,
                'all_functions' => $signatures['functions'],
                'all_types' => $signatures['types']
            ));

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('WSDL inspection failed: ', 'aito-reviews') . $e->getMessage(),
                'exception' => array(
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }

    /**
     * AJAX clear session
     */
    public function ajax_clear_session() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        $soap_client = new AITO_SOAP_Client();
        $result = $soap_client->clear_session_public();

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Session cleared successfully! Next API call will re-authenticate.', 'aito-reviews')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to clear session.', 'aito-reviews')
            ));
        }
    }

    /**
     * AJAX test stdClass fix
     */
    public function ajax_test_stdclass() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        $soap_client = new AITO_SOAP_Client();

        try {
            // Clear debug log first and verify it's cleared
            delete_option('aito_reviews_debug_log');
            $cleared_log = get_option('aito_reviews_debug_log', array());

            // Add a test marker to verify new execution
            $soap_client->log_debug('=== STDCLASS TEST STARTING ===');

            // Test getFeedbackRatingA with stdClass
            $response = $soap_client->get_feedback_rating('getFeedbackRatingA');

            // Add end marker
            $soap_client->log_debug('=== STDCLASS TEST COMPLETED ===');

            // Get fresh debug log
            $debug_log = $soap_client->get_debug_log();

            // Check for the new stdClass log message
            $stdclass_found = false;
            $encoding_errors = 0;
            $relevant_logs = array();

            foreach ($debug_log as $log_entry) {
                if (strpos($log_entry, 'Using authStruct as stdClass object') !== false) {
                    $stdclass_found = true;
                    $relevant_logs[] = $log_entry;
                }
                if (strpos($log_entry, 'object has no \'auth\' property') !== false) {
                    $encoding_errors++;
                    $relevant_logs[] = $log_entry;
                }
                if (strpos($log_entry, 'Calling with authStruct stdClass') !== false) {
                    $relevant_logs[] = $log_entry;
                }
            }

            $fix_status = $stdclass_found ? 'WORKING' : 'NOT WORKING';
            $encoding_status = $encoding_errors === 0 ? 'FIXED' : 'STILL PRESENT';

            wp_send_json_success(array(
                'message' => sprintf(
                    __('stdClass Fix Status: %s | Encoding Errors: %s', 'aito-reviews'),
                    $fix_status,
                    $encoding_status
                ),
                'stdclass_found' => $stdclass_found,
                'encoding_errors' => $encoding_errors,
                'relevant_logs' => $relevant_logs,
                'full_debug_log' => $debug_log,
                'response_received' => $response !== false,
                'fix_working' => $stdclass_found && $encoding_errors === 0,
                'debug_info' => array(
                    'log_cleared' => empty($cleared_log),
                    'total_log_entries' => count($debug_log),
                    'test_markers_found' => array(
                        'start' => count(array_filter($debug_log, function($log) { return strpos($log, 'STDCLASS TEST STARTING') !== false; })),
                        'end' => count(array_filter($debug_log, function($log) { return strpos($log, 'STDCLASS TEST COMPLETED') !== false; }))
                    )
                )
            ));

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('stdClass test failed: ', 'aito-reviews') . $e->getMessage(),
                'exception' => array(
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }
}
