<?php

// TODO wp-content/themes/absoluteescapes/lib/blocks/quote.php ($review_embed)


class AITO_Widget
{
  private $folder_name = 'aito-widget';
  private $js_filename = 'aito_widget.js';
  private $css_filename = 'aito_widget.css';

  public function __construct()
  {
    add_action('init', array($this, 'schedule_request'));
    add_action('admin_menu', array($this, 'admin_init'));
    add_filter('cron_schedules', array($this, 'every_five_mins_schedule'));
  }

  public static function activate()
  {
    $instance = new self();
    $instance->make_request();
  }

  public function schedule_request()
  {
    // Schedule the request to run every 5 minutes
    if (!wp_next_scheduled('aito_widget')) {
      wp_schedule_event(time(), 'daily', 'aito_widget');
    }
    add_action('aito_widget', array($this, 'make_request'));
  }

  public function make_request()
  {
    $this->fetch_and_save_file('https://feedback.aito.com/widget.js', $this->js_filename);
    $this->fetch_and_save_file('https://feedback.aito.com/widget.css', $this->css_filename);
  }

  private function fetch_and_save_file($url, $filename)
  {
    $response = wp_remote_get($url);

    if (is_wp_error($response)) {
      wp_mail(get_option('admin_email'), 'HTTP Request Failed', 'Error: ' . $response->get_error_message());
    } else {
      $data = wp_remote_retrieve_body($response);
      $upload_dir = wp_upload_dir();
      $folder_path = $upload_dir['basedir'] . '/' . $this->folder_name;

      if (!file_exists($folder_path)) {
        wp_mkdir_p($folder_path);
      }

      $file_path = $folder_path . '/' . $filename;
      file_put_contents($file_path, $data);
    }
  }

  public function get_data($filename)
  {
    $upload_dir = wp_upload_dir();
    $file_path = $upload_dir['basedir'] . '/' . $this->folder_name . '/' . $filename;

    if (file_exists($file_path)) {
      return file_get_contents($file_path);
    } else {
      return '<p>No data found.</p>';
    }
  }

  public function admin_init()
  {
    if (current_user_can('manage_options')) {
      add_options_page(
        'AITO Widget',
        'AITO Widget',
        'manage_options',
        'aito-widget',
        array($this, 'settings_page')
      );
    }
  }

  public function settings_page()
  {
    $js_data = $this->get_data($this->js_filename);
    $css_data = $this->get_data($this->css_filename);

    echo '<div class="wrap"><h1>AITO Widget</h1>
      <p><strong>Widget JS code:</strong></p>
      <code style="display:block; background-color:#454545; color: #ccc; padding: 20px; line-height:150%; box-model: border-box">' . esc_html($js_data) . '</code>
      <p><strong>Widget CSS code:</strong></p>
      <code style="display:block; background-color:#454545; color: #ccc; padding: 20px; line-height:150%; box-model: border-box">' . esc_html($css_data) . '</code>
    </div>';
  }

  public function every_five_mins_schedule($schedules) {
    $schedules['every_five_minutes'] = array(
      'interval' => 300, // 5 minutes in seconds (300 seconds)
      'display'  => __('Every 5 Minutes')
    );
    return $schedules;
  }

  public function output_widget()
  {
    // Use the new PHP-based AITO Reviews plugin if available
    if (function_exists('aito_has_review_data') && aito_has_review_data()) {
      $score = aito_get_review_score();
      $total = aito_get_review_total();

      // Format the output to match the expected design: "795 AITO Reviews 99% ⭐⭐⭐⭐⭐"
      $output = '<div class="aito-php-widget">';
      $output .= '<span class="aito-total">' . esc_html(number_format($total)) . ' AITO Reviews</span>';
      $output .= '<span class="aito-score">' . esc_html($score) . '%</span>';
      $output .= '<span class="aito-stars">⭐⭐⭐⭐⭐</span>';
      $output .= '</div>';

      return $output;
    }

    // Fallback to old JS widget if new plugin not available
    return '<script>var buttonTemplate = "21";</script>
    <div id="aito-widget"></div>';
  }

  public function output_css() {
    // If using new PHP widget, provide inline CSS
    if (function_exists('aito_has_review_data') && aito_has_review_data()) {
      return '<style>
        .aito-php-widget {
          display: inline-flex;
          align-items: center;
          gap: 10px;
          font-family: inherit;
          font-size: 21px;
          color: #333;
        }
        .aito-total {
          color: #333;
          font-weight: normal;
        }
        .aito-score {
          color: #333;
          font-weight: normal;
        }
        .aito-stars {
          color: #00d4aa;
          font-size: 18px;
          line-height: 1;
        }
      </style>';
    }

    // Fallback to old CSS file
    $upload_dir = wp_upload_dir();
    $css_file_url = $upload_dir['baseurl'] . '/' . $this->folder_name . '/' . $this->css_filename;
    return '<link rel="stylesheet" href="' . esc_url($css_file_url) . '">';
  }

 public function output_js() {
  // If using new PHP widget, no JavaScript needed
  if (function_exists('aito_has_review_data') && aito_has_review_data()) {
    return '<!-- AITO PHP Widget: No JavaScript required -->';
  }

  // Fallback to old JS widget
  $upload_dir = wp_upload_dir();
  $js_file_url = $upload_dir['baseurl'] . '/' . $this->folder_name . '/' . $this->js_filename;
  return '<script>var widgetKey = "431ff7cccb7181e0a5dc1d774cd70925";</script>
  <script src="' . esc_url($js_file_url) . '"></script>';
 }

}

// Register activation hook to fetch files on plugin activation
register_activation_hook(__FILE__, array('AITO_Widget', 'activate'));