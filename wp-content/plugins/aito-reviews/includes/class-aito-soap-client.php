<?php
/**
 * AITO SOAP Client Class
 * 
 * Handles SOAP communication with AITO API
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AITO_SOAP_Client {
    
    /**
     * SOAP client instance
     */
    private $soap_client;
    
    /**
     * API endpoint URL
     */
    private $endpoint_url;
    
    /**
     * Authentication credentials
     */
    private $username;
    private $password;
    private $company_id;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->endpoint_url = get_option('aito_reviews_endpoint_url', 'https://www.aito.com/api/v1/api.asmx');
        $this->username = get_option('aito_reviews_username', '');
        $this->password = get_option('aito_reviews_password', '');
        $this->company_id = get_option('aito_reviews_company_id', '');
    }
    
    /**
     * Initialize SOAP client
     */
    private function init_soap_client() {
        if ($this->soap_client !== null) {
            return true;
        }
        
        try {
            // SOAP client options
            $options = array(
                'trace' => true,
                'exceptions' => true,
                'cache_wsdl' => WSDL_CACHE_NONE,
                'connection_timeout' => 30,
                'user_agent' => 'WordPress AITO Reviews Plugin/' . AITO_REVIEWS_VERSION,
                'stream_context' => stream_context_create(array(
                    'http' => array(
                        'timeout' => 30,
                        'user_agent' => 'WordPress AITO Reviews Plugin/' . AITO_REVIEWS_VERSION
                    )
                ))
            );

            // Note: Authentication is now handled via SOAP parameters, not HTTP auth
            
            // Create SOAP client
            $wsdl_url = $this->endpoint_url . '?WSDL';
            $this->soap_client = new SoapClient($wsdl_url, $options);
            
            return true;
            
        } catch (Exception $e) {
            $this->log_error('SOAP Client Initialization Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get feedback rating data using session-aware method selection
     */
    public function get_feedback_rating($method = null) {
        if (!$this->init_soap_client()) {
            $this->log_error('Failed to initialize SOAP client');
            return false;
        }

        // Use session-aware method selection if no specific method provided
        if (!$method) {
            $method = $this->determine_method_to_use();
        }

        $this->log_debug('Using SOAP method: ' . $method);

        try {
            // Check if we need to authenticate first using login method
            if ($method === 'getFeedbackRating' && !$this->has_valid_session()) {
                $this->log_debug('No valid session, attempting login first');
                if (!$this->authenticate_with_login()) {
                    $this->log_debug('Login failed, falling back to getFeedbackRatingA');
                    return $this->get_feedback_rating('getFeedbackRatingA');
                }
            }

            // Apply session cookies if available
            if ($method === 'getFeedbackRating') {
                $this->apply_session_to_soap_client();
            }

            // Prepare SOAP request parameters based on method
            $params = array();

            if ($method === 'getFeedbackRatingA') {
                // Method with "A" suffix - requires authStruct with specific field names
                if (!empty($this->username) && !empty($this->password)) {
                    // Create authStruct as a stdClass object for proper SOAP encoding
                    $auth_struct = new stdClass();
                    $auth_struct->member_api_username = $this->username;
                    $auth_struct->member_api_password = $this->password;

                    // Pass the authStruct as the first parameter
                    $params = array($auth_struct);
                    $this->log_debug('🔧 CACHE TEST - Using authStruct as stdClass object: ' . json_encode($auth_struct));
                } else {
                    $this->log_error('getFeedbackRatingA requires username and password');
                    return false;
                }
            } else {
                // Regular methods - no auth params needed if session is valid
                $params = array();
            }

            // Add company ID if provided
            if (!empty($this->company_id)) {
                $params['companyId'] = $this->company_id;
            }

            // Log request details (mask sensitive data)
            $debug_params = $params;
            if (isset($debug_params['auth'])) {
                $debug_params['auth']['password'] = '[MASKED]';
            }
            $this->log_debug('SOAP Request Parameters: ' . json_encode($debug_params));
            $this->log_debug('SOAP Endpoint: ' . $this->endpoint_url);

            // Validate method exists in WSDL
            $available_functions = $this->soap_client->__getFunctions();
            $available_methods = $this->extract_method_names($available_functions);

            $this->log_debug('Available SOAP methods: ' . json_encode($available_methods));
            $this->log_debug('Attempting to call method: ' . $method);

            if (!in_array($method, $available_methods)) {
                $this->log_error('SOAP method not found in WSDL: ' . $method . '. Available methods: ' . json_encode($available_methods));
                return false;
            }

            $this->log_debug('Calling SOAP method: ' . $method . ' with parameters: ' . json_encode($params));

            // For getFeedbackRatingA, try multiple parameter structures
            if ($method === 'getFeedbackRatingA' && !empty($params)) {
                try {
                    $this->log_debug('Direct call with prepared stdClass parameters');

                    // Try different parameter structures for AITO API
                    $auth_struct = $params[0]; // This is our stdClass object

                    // Based on the AITO error, try .NET-style parameter binding

                    // Attempt 1: .NET-style named parameters (most likely to work)
                    try {
                        $this->log_debug('Trying .NET-style named parameters');
                        $response = $this->soap_client->getFeedbackRatingA(array(
                            'member_api_username' => $this->username,
                            'member_api_password' => $this->password
                        ));
                        $this->log_debug('Direct call successful with .NET-style named parameters');
                    } catch (SoapFault $e1) {
                        $this->log_debug('.NET-style named parameters failed: ' . $e1->getMessage());

                        // Attempt 2: Try direct method call with individual parameters
                        try {
                            $this->log_debug('Trying direct method call with individual parameters');
                            $response = $this->soap_client->getFeedbackRatingA(
                                $this->username,
                                $this->password
                            );
                            $this->log_debug('Direct call successful with individual parameters');
                        } catch (Exception $e2) {
                            $this->log_debug('Individual parameters failed: ' . $e2->getMessage());

                            // Attempt 3: Try SoapVar for proper type encoding
                            $this->log_debug('About to try SoapVar approach');
                            try {
                                $this->log_debug('Trying SoapVar for proper type encoding');
                                $auth_struct_soapvar = new SoapVar(array(
                                    'member_api_username' => $this->username,
                                    'member_api_password' => $this->password
                                ), SOAP_ENC_OBJECT, 'authStruct');

                                $response = $this->soap_client->__soapCall($method, array($auth_struct_soapvar));
                                $this->log_debug('Direct call successful with SoapVar');
                            } catch (Exception $e3) {
                                $this->log_debug('SoapVar failed: ' . $e3->getMessage());

                                // Attempt 4: Try simple associative array
                                $this->log_debug('About to try simple associative array');
                                try {
                                    $this->log_debug('Trying simple associative array');
                                    $simple_array = array(
                                        'member_api_username' => $this->username,
                                        'member_api_password' => $this->password
                                    );
                                    $response = $this->soap_client->__soapCall($method, array($simple_array));
                                    $this->log_debug('Direct call successful with simple associative array');
                                } catch (Exception $e4) {
                                    $this->log_debug('Simple associative array failed: ' . $e4->getMessage());

                                    // Attempt 5: Try the original stdClass approach (from $auth_struct)
                                    $this->log_debug('About to try original stdClass');
                                    try {
                                        $this->log_debug('Trying original stdClass as single parameter');
                                        $response = $this->soap_client->__soapCall($method, array($auth_struct));
                                        $this->log_debug('Direct call successful with stdClass as single parameter');
                                    } catch (Exception $e5) {
                                        $this->log_debug('Original stdClass failed: ' . $e5->getMessage());
                                        throw $e5; // Re-throw to trigger fallback
                                    }
                                }
                            }
                        }
                    }
                } catch (SoapFault $e) {
                    $this->log_debug('All direct call attempts failed: ' . $e->getMessage() . ', trying session-based approach');

                    // Try session-based approach since login works
                    try {
                        $this->log_debug('Attempting session-based approach with login first');

                        // First, try to login to establish session
                        $login_result = $this->authenticate_with_login();
                        if ($login_result) {
                            $this->log_debug('Login successful, now trying getFeedbackRating (without A)');
                            // After successful login, try the non-A version
                            $response = $this->soap_client->__soapCall('getFeedbackRating', array());
                            $this->log_debug('Session-based call successful');
                        } else {
                            $this->log_debug('Login failed, falling back to attempt_soap_call');
                            // Fall back to attempt_soap_call for other methods
                            $response = $this->attempt_soap_call($method, $params);
                        }
                    } catch (SoapFault $session_e) {
                        $this->log_debug('Session-based approach failed: ' . $session_e->getMessage() . ', trying final fallback');
                        // Fall back to attempt_soap_call for other methods
                        $response = $this->attempt_soap_call($method, $params);
                    }
                }
            } else {
                // Try different authentication and parameter approaches
                $response = $this->attempt_soap_call($method, $params);
            }

            // Log raw response
            $this->log_debug('SOAP Raw Response: ' . print_r($response, true));

            // Get SOAP request/response for debugging
            if (method_exists($this->soap_client, '__getLastRequest')) {
                $this->log_debug('SOAP Last Request: ' . $this->soap_client->__getLastRequest());
            }
            if (method_exists($this->soap_client, '__getLastResponse')) {
                $this->log_debug('SOAP Last Response: ' . $this->soap_client->__getLastResponse());
            }

            // Process response
            $processed_response = $this->process_feedback_response($response);

            // If this was a successful getFeedbackRatingA call, store the session
            if ($processed_response !== false && $method === 'getFeedbackRatingA') {
                $this->store_session($response);
                $this->log_debug('Session stored after successful getFeedbackRatingA authentication');
            }

            return $processed_response;

        } catch (SoapFault $e) {
            $error_details = array(
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'faultcode' => $e->faultcode ?? 'N/A',
                'faultstring' => $e->faultstring ?? 'N/A',
                'detail' => $e->detail ?? 'N/A'
            );

            // Get SOAP request/response for debugging
            if (method_exists($this->soap_client, '__getLastRequest')) {
                $error_details['last_request'] = $this->soap_client->__getLastRequest();
            }
            if (method_exists($this->soap_client, '__getLastResponse')) {
                $error_details['last_response'] = $this->soap_client->__getLastResponse();
            }

            $this->log_error('SOAP Fault: ' . json_encode($error_details));
            return false;
        } catch (Exception $e) {
            $error_details = array(
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            );

            $this->log_error('General Error: ' . json_encode($error_details));
            return false;
        }
    }
    
    /**
     * Process feedback response
     */
    private function process_feedback_response($response) {
        if (empty($response)) {
            $this->log_error('Empty response received from AITO API');
            return false;
        }
        
        // Initialize default values
        $processed_data = array(
            'score' => 0,
            'total' => 0,
            'last_updated' => current_time('timestamp'),
            'raw_response' => $response
        );
        
        try {
            // Handle different response formats
            if (is_object($response)) {
                // Convert object to array for easier processing
                $response_array = json_decode(json_encode($response), true);
            } elseif (is_string($response)) {
                // Try to parse as JSON first
                $json_response = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $response_array = $json_response;
                } else {
                    // Try to parse as XML
                    $xml_response = simplexml_load_string($response);
                    if ($xml_response !== false) {
                        $response_array = json_decode(json_encode($xml_response), true);
                    } else {
                        $response_array = array('raw' => $response);
                    }
                }
            } else {
                $response_array = (array) $response;
            }
            
            // Extract score and total from response
            $processed_data['score'] = $this->extract_score($response_array);
            $processed_data['total'] = $this->extract_total($response_array);
            
            // Log successful response
            $this->log_success('Successfully retrieved feedback rating data');
            
            return $processed_data;
            
        } catch (Exception $e) {
            $this->log_error('Error processing response: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Extract score from response
     */
    private function extract_score($response_array) {
        // Common field names for score
        $score_fields = array('score', 'rating', 'average', 'averageRating', 'feedbackScore');
        
        foreach ($score_fields as $field) {
            if (isset($response_array[$field])) {
                return floatval($response_array[$field]);
            }
        }
        
        // Try nested structures
        if (isset($response_array['result'])) {
            foreach ($score_fields as $field) {
                if (isset($response_array['result'][$field])) {
                    return floatval($response_array['result'][$field]);
                }
            }
        }
        
        if (isset($response_array['data'])) {
            foreach ($score_fields as $field) {
                if (isset($response_array['data'][$field])) {
                    return floatval($response_array['data'][$field]);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * Extract total from response
     */
    private function extract_total($response_array) {
        // Common field names for total
        $total_fields = array('total', 'count', 'totalReviews', 'reviewCount', 'feedbackCount');
        
        foreach ($total_fields as $field) {
            if (isset($response_array[$field])) {
                return intval($response_array[$field]);
            }
        }
        
        // Try nested structures
        if (isset($response_array['result'])) {
            foreach ($total_fields as $field) {
                if (isset($response_array['result'][$field])) {
                    return intval($response_array['result'][$field]);
                }
            }
        }
        
        if (isset($response_array['data'])) {
            foreach ($total_fields as $field) {
                if (isset($response_array['data'][$field])) {
                    return intval($response_array['data'][$field]);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * Test connection to AITO API
     */
    public function test_connection() {
        if (!$this->init_soap_client()) {
            return array(
                'success' => false,
                'message' => 'Failed to initialize SOAP client'
            );
        }
        
        try {
            // Try to get WSDL functions
            $functions = $this->soap_client->__getFunctions();
            
            if (empty($functions)) {
                return array(
                    'success' => false,
                    'message' => 'No SOAP functions available'
                );
            }
            
            // Extract actual method names from function signatures
            $method_names = $this->extract_method_names($functions);

            // Check for available review methods
            $review_methods = array();
            $feedback_methods = array();

            foreach ($method_names as $method_name) {
                // Look for feedback/review related methods
                if (preg_match('/feedback|review|rating/i', $method_name)) {
                    $feedback_methods[] = $method_name;
                }

                // Specifically look for getFeedbackRating methods
                if (preg_match('/getFeedbackRating/i', $method_name)) {
                    $review_methods[] = $method_name;
                }
            }

            $this->log_debug('Available feedback methods: ' . json_encode($feedback_methods));
            $this->log_debug('Available review methods: ' . json_encode($review_methods));
            
            return array(
                'success' => true,
                'message' => 'Connection successful',
                'functions' => $functions,
                'method_names' => $method_names,
                'feedback_methods' => $feedback_methods,
                'review_methods' => $review_methods,
                'total_methods' => count($functions),
                'total_method_names' => count($method_names),
                'feedback_count' => count($feedback_methods),
                'review_count' => count($review_methods)
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Log error message
     */
    private function log_error($message) {
        update_option('aito_reviews_last_error', current_time('mysql') . ': ' . $message);
        
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('AITO Reviews Plugin Error: ' . $message);
        }
    }
    
    /**
     * Log success message
     */
    private function log_success($message) {
        update_option('aito_reviews_last_error', ''); // Clear previous errors

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('AITO Reviews Plugin Success: ' . $message);
        }
    }

    /**
     * Log debug message
     */
    public function log_debug($message) {
        // Store debug messages in a separate option for admin viewing
        $debug_log = get_option('aito_reviews_debug_log', array());
        $debug_log[] = current_time('mysql') . ': ' . $message;

        // Keep only last 20 debug entries
        if (count($debug_log) > 20) {
            $debug_log = array_slice($debug_log, -20);
        }

        update_option('aito_reviews_debug_log', $debug_log);

        // Also write to a dedicated file for debugging
        $this->write_to_debug_file($message);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('AITO Reviews Plugin Debug: ' . $message);
        }
    }

    /**
     * Write debug message to dedicated file
     */
    private function write_to_debug_file($message) {
        $log_file = WP_CONTENT_DIR . '/aito-debug.log';
        $timestamp = current_time('mysql');
        $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;

        // Write to file (append mode)
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Get debug log
     */
    public function get_debug_log() {
        return get_option('aito_reviews_debug_log', array());
    }

    /**
     * Clear debug log
     */
    public function clear_debug_log() {
        // Always return true - either we deleted the option or it didn't exist
        delete_option('aito_reviews_debug_log');
        return true;
    }

    /**
     * Extract method names from WSDL function signatures
     */
    private function extract_method_names($functions) {
        $method_names = array();

        foreach ($functions as $function) {
            // Extract method name from function signature
            // Example: "getFeedbackRatingAResponse getFeedbackRatingA(getFeedbackRatingA $parameters)"
            if (preg_match('/(\w+)\s+(\w+)\s*\(/', $function, $matches)) {
                $method_names[] = $matches[2]; // Second match is the method name
            } elseif (preg_match('/(\w+)\s*\(/', $function, $matches)) {
                $method_names[] = $matches[1]; // First match if simpler format
            }
        }

        return array_unique($method_names);
    }

    /**
     * Get available SOAP method names
     */
    public function get_available_methods() {
        if (!$this->init_soap_client()) {
            return array();
        }

        try {
            $functions = $this->soap_client->__getFunctions();
            return $this->extract_method_names($functions);
        } catch (Exception $e) {
            $this->log_error('Failed to get available methods: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Attempt SOAP call with different authentication methods
     */
    private function attempt_soap_call($method, $params) {
        $attempts = array();

        // Attempt 0: Try without authentication first (for methods that don't require auth)
        try {
            $this->log_debug('Attempt 0: No authentication - testing if method requires auth');

            // Create clean parameters without auth
            $clean_params = array();
            if (!empty($this->company_id)) {
                $clean_params['companyId'] = $this->company_id;
            }

            if (!empty($clean_params)) {
                $response = $this->soap_client->__soapCall($method, array($clean_params));
            } else {
                $response = $this->soap_client->__soapCall($method, array());
            }

            $this->log_debug('Attempt 0: Success - Method does NOT require authentication!');
            return $response;
        } catch (SoapFault $e) {
            $attempts[] = 'Attempt 0 (no auth) failed: ' . $e->getMessage();
            $this->log_debug('Attempt 0 failed: ' . $e->getMessage() . ' - Method likely requires authentication');
        }

        // Attempt 1: Try with proper authStruct for methods ending with 'A'
        if (substr($method, -1) === 'A' && !empty($this->username) && !empty($this->password)) {
            // Use stdClass object for proper SOAP encoding
            try {
                $this->log_debug('Attempt 1: Using authStruct as stdClass object for method ending with A');

                $auth_struct = new stdClass();
                $auth_struct->member_api_username = $this->username;
                $auth_struct->member_api_password = $this->password;

                $this->log_debug('Attempt 1: Calling with authStruct stdClass: ' . json_encode($auth_struct));
                $response = $this->soap_client->__soapCall($method, array($auth_struct));
                $this->log_debug('Attempt 1: Success with authStruct stdClass');
                return $response;
            } catch (SoapFault $e) {
                $attempts[] = 'Attempt 1 (authStruct stdClass) failed: ' . $e->getMessage();
                $this->log_debug('Attempt 1 failed: ' . $e->getMessage());
            }
        }

        // Attempt 2: Direct method call with parameters as individual arguments
        try {
            $this->log_debug('Attempt 2: Direct method call with individual parameters');
            if (!empty($params)) {
                $response = $this->soap_client->__soapCall($method, $params);
            } else {
                $response = $this->soap_client->__soapCall($method, array());
            }
            $this->log_debug('Attempt 2: Success');
            return $response;
        } catch (SoapFault $e) {
            $attempts[] = 'Attempt 2 failed: ' . $e->getMessage();
            $this->log_debug('Attempt 2 failed: ' . $e->getMessage());
        }

        // Attempt 3: Parameters as single array
        try {
            $this->log_debug('Attempt 3: Parameters as single array');
            $response = $this->soap_client->__soapCall($method, array($params));
            $this->log_debug('Attempt 3: Success');
            return $response;
        } catch (SoapFault $e) {
            $attempts[] = 'Attempt 3 failed: ' . $e->getMessage();
            $this->log_debug('Attempt 3 failed: ' . $e->getMessage());
        }

        // Attempt 3: Use SOAP headers for authentication
        try {
            $this->log_debug('Attempt 3: Using SOAP headers for authentication');

            if (!empty($this->username) && !empty($this->password)) {
                // Create authentication header
                $auth_header = new SoapHeader(
                    'http://api.aito.co.uk/',
                    'AuthHeader',
                    array(
                        'Username' => $this->username,
                        'Password' => $this->password
                    )
                );

                $this->soap_client->__setSoapHeaders($auth_header);
            }

            // Call without auth parameters in body
            $clean_params = array();
            if (!empty($this->company_id)) {
                $clean_params['companyId'] = $this->company_id;
            }

            $response = $this->soap_client->__soapCall($method, array($clean_params));
            $this->log_debug('Attempt 3: Success');
            return $response;
        } catch (SoapFault $e) {
            $attempts[] = 'Attempt 3 failed: ' . $e->getMessage();
            $this->log_debug('Attempt 3 failed: ' . $e->getMessage());
        }

        // Attempt 4: Try with HTTP Basic Authentication in SOAP client options
        try {
            $this->log_debug('Attempt 4: Reinitializing with HTTP Basic Auth');

            // Reinitialize SOAP client with HTTP auth
            $options = array(
                'trace' => true,
                'exceptions' => true,
                'cache_wsdl' => WSDL_CACHE_NONE,
                'connection_timeout' => 30,
                'user_agent' => 'WordPress AITO Reviews Plugin/' . AITO_REVIEWS_VERSION,
                'stream_context' => stream_context_create(array(
                    'http' => array(
                        'timeout' => 30,
                        'user_agent' => 'WordPress AITO Reviews Plugin/' . AITO_REVIEWS_VERSION
                    )
                ))
            );

            if (!empty($this->username) && !empty($this->password)) {
                $options['login'] = $this->username;
                $options['password'] = $this->password;
            }

            $wsdl_url = $this->endpoint_url . '?WSDL';
            $temp_client = new SoapClient($wsdl_url, $options);

            // Call without auth parameters in body
            $clean_params = array();
            if (!empty($this->company_id)) {
                $clean_params['companyId'] = $this->company_id;
            }

            $response = $temp_client->__soapCall($method, array($clean_params));
            $this->log_debug('Attempt 4: Success');
            return $response;
        } catch (Exception $e) {
            $attempts[] = 'Attempt 4 failed: ' . $e->getMessage();
            $this->log_debug('Attempt 4 failed: ' . $e->getMessage());
        }

        // Attempt 6: Try login method first with proper validation
        try {
            $this->log_debug('Attempt 6: Trying login method first');

            if (!empty($this->username) && !empty($this->password)) {
                // Try to find and call a login method
                $available_methods = $this->extract_method_names($this->soap_client->__getFunctions());
                $login_methods = array('login', 'Login', 'authenticate', 'Authenticate', 'logon', 'Logon');

                foreach ($login_methods as $login_method) {
                    if (in_array($login_method, $available_methods)) {
                        $this->log_debug('Found login method: ' . $login_method);

                        // Attempt login
                        $login_params = array(
                            'username' => $this->username,
                            'password' => $this->password
                        );

                        try {
                            $login_response = $this->soap_client->__soapCall($login_method, array($login_params));
                            $this->log_debug('Login response received: ' . print_r($login_response, true));

                            // Check if login was actually successful
                            $login_successful = false;
                            if (is_object($login_response)) {
                                // Check for common success indicators
                                if (isset($login_response->loginResult)) {
                                    $login_successful = ($login_response->loginResult !== 'authInvalid' &&
                                                       $login_response->loginResult !== 'failed' &&
                                                       $login_response->loginResult !== false);
                                } elseif (isset($login_response->result)) {
                                    $login_successful = ($login_response->result === 'success' ||
                                                       $login_response->result === true);
                                } else {
                                    // If no specific result field, assume success if no exception was thrown
                                    $login_successful = true;
                                }
                            } elseif (is_array($login_response)) {
                                $login_successful = (!isset($login_response['loginResult']) ||
                                                   $login_response['loginResult'] !== 'authInvalid');
                            } else {
                                // For other response types, assume success if no exception
                                $login_successful = true;
                            }

                            if ($login_successful) {
                                $this->log_debug('Login validation successful');

                                // Now try the original method call
                                $clean_params = array();
                                if (!empty($this->company_id)) {
                                    $clean_params['companyId'] = $this->company_id;
                                }

                                $response = $this->soap_client->__soapCall($method, array($clean_params));
                                $this->log_debug('Attempt 6: Success after validated login');
                                return $response;
                            } else {
                                $this->log_debug('Login method ' . $login_method . ' returned invalid auth: ' . print_r($login_response, true));
                                continue;
                            }
                        } catch (SoapFault $login_error) {
                            $this->log_debug('Login method ' . $login_method . ' failed: ' . $login_error->getMessage());
                            continue;
                        }
                    }
                }
            }

            $attempts[] = 'Attempt 6 failed: No suitable login method found or login failed';
        } catch (Exception $e) {
            $attempts[] = 'Attempt 6 failed: ' . $e->getMessage();
            $this->log_debug('Attempt 6 failed: ' . $e->getMessage());
        }

        // All attempts failed
        $this->log_error('All SOAP call attempts failed: ' . json_encode($attempts));
        throw new SoapFault('Client', 'All authentication methods failed. Attempts: ' . implode('; ', $attempts));
    }

    /**
     * Test which methods require authentication with improved auth handling
     */
    public function test_method_auth_requirements() {
        if (!$this->init_soap_client()) {
            return array('error' => 'Failed to initialize SOAP client');
        }

        $available_methods = $this->extract_method_names($this->soap_client->__getFunctions());
        $feedback_methods = array();

        // Filter to feedback/review methods only
        foreach ($available_methods as $method_name) {
            if (preg_match('/feedback|review|rating/i', $method_name)) {
                $feedback_methods[] = $method_name;
            }
        }

        $results = array();

        foreach ($feedback_methods as $method) {
            $this->log_debug('Testing auth requirements for method: ' . $method);

            try {
                // Test without authentication first
                $clean_params = array();
                if (!empty($this->company_id)) {
                    $clean_params['companyId'] = $this->company_id;
                }

                if (!empty($clean_params)) {
                    $response = $this->soap_client->__soapCall($method, array($clean_params));
                } else {
                    $response = $this->soap_client->__soapCall($method, array());
                }

                $results[$method] = array(
                    'requires_auth' => false,
                    'status' => 'success',
                    'message' => 'Method works without authentication',
                    'response_type' => gettype($response),
                    'response_preview' => is_object($response) || is_array($response) ?
                        json_encode($response) : substr(strval($response), 0, 100)
                );

            } catch (SoapFault $e) {
                // Check if error indicates authentication is required
                $error_message = $e->getMessage();
                $requires_auth = (
                    strpos($error_message, 'not logged in') !== false ||
                    strpos($error_message, 'authentication') !== false ||
                    strpos($error_message, 'unauthorized') !== false ||
                    strpos($error_message, 'login') !== false ||
                    strpos($error_message, 'auth') !== false ||
                    strpos($error_message, 'member_api_username') !== false
                );

                // If method requires auth, try with proper auth structure
                if ($requires_auth && !empty($this->username) && !empty($this->password)) {
                    try {
                        $auth_response = $this->attempt_authenticated_call($method);
                        if ($auth_response !== false) {
                            $results[$method] = array(
                                'requires_auth' => true,
                                'status' => 'success_with_auth',
                                'message' => 'Method works with authentication',
                                'response_type' => gettype($auth_response),
                                'response_preview' => is_object($auth_response) || is_array($auth_response) ?
                                    json_encode($auth_response) : substr(strval($auth_response), 0, 100)
                            );
                        } else {
                            $results[$method] = array(
                                'requires_auth' => true,
                                'status' => 'auth_failed',
                                'message' => 'Method requires authentication but auth failed: ' . $error_message,
                                'error_code' => $e->getCode(),
                                'fault_code' => $e->faultcode ?? 'N/A'
                            );
                        }
                    } catch (Exception $auth_e) {
                        $results[$method] = array(
                            'requires_auth' => true,
                            'status' => 'auth_error',
                            'message' => 'Auth attempt failed: ' . $auth_e->getMessage(),
                            'original_error' => $error_message,
                            'error_code' => $e->getCode(),
                            'fault_code' => $e->faultcode ?? 'N/A'
                        );
                    }
                } else {
                    $results[$method] = array(
                        'requires_auth' => $requires_auth,
                        'status' => 'failed',
                        'message' => $error_message,
                        'error_code' => $e->getCode(),
                        'fault_code' => $e->faultcode ?? 'N/A'
                    );
                }
            } catch (Exception $e) {
                $results[$method] = array(
                    'requires_auth' => 'unknown',
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'error_code' => $e->getCode()
                );
            }
        }

        return $results;
    }

    /**
     * Attempt authenticated call with proper auth structure
     */
    private function attempt_authenticated_call($method) {
        if (empty($this->username) || empty($this->password)) {
            return false;
        }

        try {
            // For methods ending with 'A', use the correct authStruct format
            if (substr($method, -1) === 'A') {
                // Create authStruct as stdClass object for proper SOAP encoding
                $auth_struct = new stdClass();
                $auth_struct->member_api_username = $this->username;
                $auth_struct->member_api_password = $this->password;

                $this->log_debug('Trying authStruct as stdClass object: ' . json_encode($auth_struct));
                return $this->soap_client->__soapCall($method, array($auth_struct));
            } else {
                // For other methods, try different auth approaches
                $auth_params = array(
                    'username' => $this->username,
                    'password' => $this->password
                );

                if (!empty($this->company_id)) {
                    $auth_params['companyId'] = $this->company_id;
                }

                return $this->soap_client->__soapCall($method, array($auth_params));
            }
        } catch (Exception $e) {
            $this->log_debug('All authenticated call attempts failed for ' . $method . ': ' . $e->getMessage());
            return false;
        }

        return false;
    }

    /**
     * Determine which method to use based on session state
     */
    private function determine_method_to_use() {
        // Check if we have a valid session
        if ($this->has_valid_session()) {
            $this->log_debug('Valid session found, using getFeedbackRating');
            return 'getFeedbackRating';
        } else {
            $this->log_debug('No valid session, will attempt login first or use getFeedbackRatingA');
            return 'getFeedbackRating'; // Try login first, fallback to A method
        }
    }

    /**
     * Authenticate using the login method (cookie-based authentication)
     */
    private function authenticate_with_login() {
        if (empty($this->username) || empty($this->password)) {
            $this->log_debug('No credentials provided for login method');
            return false;
        }

        try {
            $this->log_debug('Attempting login method for cookie-based authentication');

            // Call the login method with username and password
            $login_params = array(
                'username' => $this->username,
                'password' => $this->password
            );

            $login_response = $this->soap_client->__soapCall('login', array($login_params));
            $this->log_debug('Login method response: ' . print_r($login_response, true));

            // Check the authResultEnum response
            $auth_result = null;
            if (is_object($login_response) && isset($login_response->loginResult)) {
                $auth_result = $login_response->loginResult;
            } elseif (is_numeric($login_response)) {
                $auth_result = (int)$login_response;
            }

            // According to AITO docs, but API returns strings instead of numbers:
            // authOK (0) - Authentication successful
            // authInvalid (1) - Invalid username/password
            // authDisabled (2) - Account disabled

            $this->log_debug('Auth result value: ' . print_r($auth_result, true) . ' (type: ' . gettype($auth_result) . ')');

            if ($auth_result === 0 || $auth_result === 'authOK') {
                $this->log_debug('Login successful (authOK), storing session');
                $this->store_session_from_login();
                return true;
            } elseif ($auth_result === 1 || $auth_result === 'authInvalid') {
                $this->log_error('Login failed: Invalid username or password (authInvalid)');
                return false;
            } elseif ($auth_result === 2 || $auth_result === 'authDisabled') {
                $this->log_error('Login failed: Account disabled (authDisabled)');
                return false;
            } else {
                $this->log_error('Login failed: Unknown response - ' . print_r($login_response, true));
                return false;
            }

        } catch (SoapFault $e) {
            $this->log_error('Login method failed with SOAP fault: ' . $e->getMessage());
            return false;
        } catch (Exception $e) {
            $this->log_error('Login method failed with exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Store session after successful login method
     */
    private function store_session_from_login() {
        $session_data = array(
            'timestamp' => time(),
            'authenticated' => true,
            'method' => 'login',
            'endpoint' => $this->endpoint_url,
            'username' => $this->username
        );

        // Extract cookies from SOAP client
        if ($this->soap_client && method_exists($this->soap_client, '__getLastResponseHeaders')) {
            $headers = $this->soap_client->__getLastResponseHeaders();
            if ($headers && preg_match_all('/Set-Cookie:\s*([^;]+)/i', $headers, $matches)) {
                $session_data['cookies'] = $matches[1];
                $this->log_debug('Stored ' . count($matches[1]) . ' session cookies from login');
            }
        }

        update_option('aito_reviews_session', $session_data);
        $this->log_debug('Session stored successfully after login method');
    }

    /**
     * Check if we have a valid AITO session
     */
    private function has_valid_session() {
        $session_data = get_option('aito_reviews_session', array());

        if (empty($session_data)) {
            return false;
        }

        // Check if session has expired (default 1 hour)
        $session_timeout = 3600; // 1 hour in seconds
        $current_time = time();

        if (isset($session_data['timestamp']) &&
            ($current_time - $session_data['timestamp']) < $session_timeout) {
            return true;
        }

        // Session expired, clear it
        $this->clear_session();
        return false;
    }

    /**
     * Store session data after successful authentication
     */
    private function store_session($response = null) {
        $session_data = array(
            'timestamp' => time(),
            'authenticated' => true,
            'endpoint' => $this->endpoint_url,
            'username' => $this->username
        );

        // Store any session cookies from the SOAP response
        if ($this->soap_client && method_exists($this->soap_client, '__getLastResponseHeaders')) {
            $headers = $this->soap_client->__getLastResponseHeaders();
            if ($headers && preg_match('/Set-Cookie: ([^;]+)/i', $headers, $matches)) {
                $session_data['cookie'] = $matches[1];
                $this->log_debug('Stored session cookie: ' . $matches[1]);
            }
        }

        update_option('aito_reviews_session', $session_data);
        $this->log_debug('Session stored successfully');
    }

    /**
     * Clear stored session data
     */
    private function clear_session() {
        delete_option('aito_reviews_session');
        $this->log_debug('Session cleared');
    }

    /**
     * Apply stored session cookies to SOAP client
     */
    private function apply_session_to_soap_client() {
        $session_data = get_option('aito_reviews_session', array());

        if (!empty($session_data['cookies']) && $this->soap_client) {
            // Build cookie header from stored cookies
            $cookie_header = 'Cookie: ' . implode('; ', $session_data['cookies']);

            // Set cookie in SOAP client context
            $context_options = array(
                'http' => array(
                    'header' => $cookie_header
                )
            );

            $context = stream_context_create($context_options);

            // Reinitialize SOAP client with session cookies
            try {
                $options = array(
                    'trace' => true,
                    'exceptions' => true,
                    'cache_wsdl' => WSDL_CACHE_NONE,
                    'connection_timeout' => 30,
                    'user_agent' => 'WordPress AITO Reviews Plugin/' . AITO_REVIEWS_VERSION,
                    'stream_context' => $context
                );

                $wsdl_url = $this->endpoint_url . '?WSDL';
                $this->soap_client = new SoapClient($wsdl_url, $options);

                $this->log_debug('Applied session cookies to SOAP client: ' . $cookie_header);
                return true;
            } catch (Exception $e) {
                $this->log_error('Failed to apply session cookies: ' . $e->getMessage());
                $this->clear_session();
                return false;
            }
        } elseif (!empty($session_data['cookie']) && $this->soap_client) {
            // Fallback for single cookie format
            $context_options = array(
                'http' => array(
                    'header' => 'Cookie: ' . $session_data['cookie']
                )
            );

            $context = stream_context_create($context_options);

            try {
                $options = array(
                    'trace' => true,
                    'exceptions' => true,
                    'cache_wsdl' => WSDL_CACHE_NONE,
                    'connection_timeout' => 30,
                    'user_agent' => 'WordPress AITO Reviews Plugin/' . AITO_REVIEWS_VERSION,
                    'stream_context' => $context
                );

                $wsdl_url = $this->endpoint_url . '?WSDL';
                $this->soap_client = new SoapClient($wsdl_url, $options);

                $this->log_debug('Applied single session cookie to SOAP client');
                return true;
            } catch (Exception $e) {
                $this->log_error('Failed to apply single session cookie: ' . $e->getMessage());
                $this->clear_session();
                return false;
            }
        }

        $this->log_debug('No session cookies to apply');
        return false;
    }

    /**
     * Get WSDL method signatures for debugging
     */
    public function get_method_signatures() {
        if (!$this->init_soap_client()) {
            return array('error' => 'Failed to initialize SOAP client');
        }

        try {
            $functions = $this->soap_client->__getFunctions();
            $types = $this->soap_client->__getTypes();

            return array(
                'functions' => $functions,
                'types' => $types,
                'endpoint' => $this->endpoint_url
            );
        } catch (Exception $e) {
            return array('error' => 'Failed to get WSDL info: ' . $e->getMessage());
        }
    }

    /**
     * Get session status for admin display
     */
    public function get_session_status() {
        $session_data = get_option('aito_reviews_session', array());

        if (empty($session_data)) {
            return array(
                'has_session' => false,
                'message' => 'No active session'
            );
        }

        $current_time = time();
        $session_age = $current_time - $session_data['timestamp'];
        $session_timeout = 3600; // 1 hour

        if ($session_age < $session_timeout) {
            $remaining_time = $session_timeout - $session_age;
            $auth_method = $session_data['method'] ?? 'unknown';
            $cookie_count = isset($session_data['cookies']) ? count($session_data['cookies']) :
                           (isset($session_data['cookie']) ? 1 : 0);

            return array(
                'has_session' => true,
                'message' => 'Active session (' . $auth_method . ' method)',
                'authenticated_at' => date('Y-m-d H:i:s', $session_data['timestamp']),
                'expires_in' => gmdate('H:i:s', $remaining_time),
                'username' => $session_data['username'] ?? 'Unknown',
                'auth_method' => $auth_method,
                'cookie_count' => $cookie_count
            );
        } else {
            return array(
                'has_session' => false,
                'message' => 'Session expired',
                'expired_at' => date('Y-m-d H:i:s', $session_data['timestamp'] + $session_timeout)
            );
        }
    }

    /**
     * Clear session (public method for admin use)
     */
    public function clear_session_public() {
        $this->clear_session();
        return true;
    }
}
